# Konfigurasi WhatsApp Dinamis

Sistem WhatsApp dinamis memungkinkan setiap lowongan kerja terhubung dengan admin WhatsApp yang berbeda berdasarkan lokasi (provinsi dan kabupaten).

## Cara Kerja

1. **Prioritas Pencarian:**
   - Pertama: Cari berdasarkan `provinsi|kabupaten` (contoh: "Jawa Barat|Bandung")
   - Kedua: Jika tidak ditemukan, cari berdasarkan `provinsi` saja
   - Ketiga: Jika tidak ditemukan, gunakan konfigurasi default

2. **File Konfigurasi:** `config/whatsapp.js`

## Struktur Konfigurasi

```javascript
export const whatsappConfig = {
  // Default/fallback admin
  default: {
    number: '6287777733566',
    name: 'Admin Recruitment Tata Karya Gemilang. PT'
  },
  
  // Mapping berdasarkan provinsi dan kabupaten
  locations: {
    'Provinsi|Kabupaten': {
      number: 'nomor_whatsapp',
      name: '<PERSON>a Admin'
    }
  },
  
  // Mapping berdasarkan provinsi saja
  provinces: {
    'Provinsi': {
      number: 'nomor_whatsapp',
      name: '<PERSON>a Admin'
    }
  }
}
```

## Cara Menambah/Mengubah Konfigurasi

### 1. Menambah Admin untuk Lokasi Spesifik

Edit file `config/whatsapp.js`, tambahkan di bagian `locations`:

```javascript
locations: {
  // Existing entries...
  'Jawa Timur|Malang': {
    number: '6281234567999',
    name: 'Admin Malang'
  }
}
```

### 2. Menambah Admin untuk Provinsi

Edit file `config/whatsapp.js`, tambahkan di bagian `provinces`:

```javascript
provinces: {
  // Existing entries...
  'Bali': {
    number: '6281234567888',
    name: 'Admin Bali'
  }
}
```

### 3. Mengubah Admin Default

Edit file `config/whatsapp.js`, ubah bagian `default`:

```javascript
default: {
  number: '6281234567000',
  name: 'Admin Pusat'
}
```

## Format Nomor WhatsApp

- Gunakan format internasional tanpa tanda `+`
- Contoh: `6287777733566` (untuk nomor Indonesia)
- Pastikan nomor sudah terdaftar di WhatsApp

## Contoh Penggunaan

### Data Lowongan:
```json
{
  "judul": "Software Engineer",
  "provinsi": "Jawa Barat",
  "kabupaten": "Bandung"
}
```

### Proses Pencarian Admin:
1. Cari `"Jawa Barat|Bandung"` di `locations` ✅ Ditemukan
2. Gunakan admin: "Admin Bandung" dengan nomor "6281234567894"

### Jika tidak ada konfigurasi spesifik:
1. Cari `"Jawa Barat|Bandung"` di `locations` ❌ Tidak ditemukan
2. Cari `"Jawa Barat"` di `provinces` ✅ Ditemukan
3. Gunakan admin: "Admin Jawa Barat" dengan nomor "6281234567802"

### Jika sama sekali tidak ada:
1. Cari `"Jawa Barat|Bandung"` di `locations` ❌ Tidak ditemukan
2. Cari `"Jawa Barat"` di `provinces` ❌ Tidak ditemukan
3. Gunakan admin default: "Admin Recruitment Tata Karya Gemilang. PT"

## Testing

Untuk menguji konfigurasi:

1. Buka halaman detail lowongan
2. Lihat teks "Anda akan terhubung dengan: [Nama Admin]"
3. Klik tombol "Lamar via WhatsApp"
4. Periksa apakah nomor WhatsApp yang terbuka sesuai dengan konfigurasi

## Tips

1. **Konsistensi Nama:** Pastikan nama provinsi dan kabupaten di konfigurasi sama persis dengan data di `data.json`
2. **Testing:** Selalu test setelah menambah konfigurasi baru
3. **Backup:** Backup file konfigurasi sebelum melakukan perubahan besar
4. **Dokumentasi:** Update dokumentasi ini jika ada perubahan struktur
