<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        <PERSON><PERSON><PERSON> <span class="text-blue-600">I<PERSON><PERSON></span>
      </h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        Ribuan lowongan kerja dari berbagai perusahaan terpercaya menanti Anda
      </p>
    </div>

    <!-- Search Bar -->
    <SearchBar :jobs="allJobs" @filter="handleFilter" />

    <!-- Education & Gender Tags -->
    <div class="mb-12">
      <EducationGenderTags 
        :jobs="allJobs" 
        @filterEducation="handleEducationFilter"
        @filterGender="handleGenderFilter"
      />
    </div>

    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
      <div class="text-center p-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="text-3xl font-bold text-blue-600 mb-2">{{ totalJobs }}</div>
        <div class="text-gray-600">Total Lowongan</div>
      </div>
      <div class="text-center p-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="text-3xl font-bold text-green-600 mb-2">{{ totalProvinsi }}</div>
        <div class="text-gray-600">Provinsi</div>
      </div>
      <div class="text-center p-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="text-3xl font-bold text-purple-600 mb-2">{{ totalPendidikan }}</div>
        <div class="text-gray-600">Tingkat Pendidikan</div>
      </div>
      <div class="text-center p-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="text-3xl font-bold text-orange-600 mb-2">{{ totalDivisi }}</div>
        <div class="text-gray-600">Divisi</div>
      </div>
    </div>

    <!-- Job Listings -->
    <div class="mb-8">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-semibold text-gray-900">
          {{ hasActiveFilter ? 'Hasil Pencarian' : 'Semua Lowongan' }}
        </h2>
        <div class="text-sm text-gray-500">
          Menampilkan {{ filteredJobs.length }} dari {{ totalJobs }} lowongan
        </div>
      </div>

      <!-- Filter Info -->
      <div v-if="hasActiveFilter" class="mb-6">
        <div class="flex flex-wrap gap-2">
          <span v-if="currentFilter.provinsi" class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
            {{ currentFilter.provinsi }}
            <button @click="removeFilter('provinsi')" class="ml-2 text-blue-600 hover:text-blue-800">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </span>
          <span v-if="currentFilter.kabupaten" class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
            {{ currentFilter.kabupaten }}
            <button @click="removeFilter('kabupaten')" class="ml-2 text-green-600 hover:text-green-800">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </span>
          <span v-if="currentFilter.pendidikan" class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
            {{ currentFilter.pendidikan }}
            <button @click="removeFilter('pendidikan')" class="ml-2 text-purple-600 hover:text-purple-800">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </span>
          <span v-if="currentFilter.jenisKelamin" class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-pink-100 text-pink-800">
            {{ currentFilter.jenisKelamin }}
            <button @click="removeFilter('jenisKelamin')" class="ml-2 text-pink-600 hover:text-pink-800">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </span>
        </div>
      </div>

      <!-- Jobs Grid -->
      <div v-if="filteredJobs.length > 0" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-4">
        <JobCard
          v-for="job in filteredJobs"
          :key="job.id"
          :judul="job.judul"
          :provinsi="job.provinsi"
          :kabupaten="job.kabupaten"
          :deadline="job.deadline"
          :jenis="job.jenis"
          :divisi="job.divisi"
          :slug="job.slug"
          :pendidikan="job.pendidikan"
          :jenisKelamin="job.jenisKelamin"
          :gambar="job.gambar"
          :lokasi="job.lokasi"
        />
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada lowongan ditemukan</h3>
        <p class="text-gray-500 mb-4">Coba ubah filter pencarian Anda</p>
        <button @click="resetAllFilters" class="btn-primary">
          Reset Semua Filter
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Page meta
definePageMeta({
  title: 'Lowongan Kerja - JobPortal'
})

// Import data
const { default: jobsData } = await import('~/data/data.json')
const allJobs = ref(jobsData)

// Reactive data
const currentFilter = ref({
  provinsi: '',
  kabupaten: '',
  pendidikan: '',
  jenisKelamin: ''
})

// Computed properties
const filteredJobs = computed(() => {
  let jobs = allJobs.value
  
  if (currentFilter.value.provinsi) {
    jobs = jobs.filter(job => job.provinsi === currentFilter.value.provinsi)
  }

  if (currentFilter.value.kabupaten) {
    jobs = jobs.filter(job => job.kabupaten === currentFilter.value.kabupaten)
  }

  if (currentFilter.value.pendidikan) {
    jobs = jobs.filter(job => job.pendidikan === currentFilter.value.pendidikan)
  }

  if (currentFilter.value.jenisKelamin) {
    jobs = jobs.filter(job => job.jenisKelamin === currentFilter.value.jenisKelamin)
  }
  
  return jobs
})

const hasActiveFilter = computed(() => {
  return currentFilter.value.provinsi || 
         currentFilter.value.kabupaten || 
         currentFilter.value.pendidikan || 
         currentFilter.value.jenisKelamin
})

const totalJobs = computed(() => allJobs.value.length)

const totalProvinsi = computed(() => {
  const provinsi = [...new Set(allJobs.value.map(job => job.provinsi))]
  return provinsi.length
})

const totalPendidikan = computed(() => {
  const pendidikan = [...new Set(allJobs.value.map(job => job.pendidikan))]
  return pendidikan.length
})

const totalDivisi = computed(() => {
  const divisi = [...new Set(allJobs.value.map(job => job.divisi))]
  return divisi.length
})

// Methods
const handleFilter = (filter) => {
  currentFilter.value = { ...filter }
}

const handleEducationFilter = (pendidikan) => {
  currentFilter.value.pendidikan = pendidikan
}

const handleGenderFilter = (jenisKelamin) => {
  currentFilter.value.jenisKelamin = jenisKelamin
}

const removeFilter = (type) => {
  if (type === 'provinsi') {
    currentFilter.value.provinsi = ''
    currentFilter.value.kabupaten = ''
  } else if (type === 'kabupaten') {
    currentFilter.value.kabupaten = ''
  } else if (type === 'pendidikan') {
    currentFilter.value.pendidikan = ''
  } else if (type === 'jenisKelamin') {
    currentFilter.value.jenisKelamin = ''
  }
}

const resetAllFilters = () => {
  currentFilter.value = {
    provinsi: '',
    kabupaten: '',
    pendidikan: '',
    jenisKelamin: ''
  }
}

// SEO
useHead({
  title: 'Lowongan Kerja - Cari Pekerjaan Terpercaya | JobPortal',
  meta: [
    {
      name: 'description',
      content: 'Jelajahi ribuan lowongan kerja dari berbagai perusahaan terpercaya di Indonesia. Temukan pekerjaan impian Anda dengan filter lokasi, pendidikan, dan kriteria lainnya.'
    }
  ]
})
</script>