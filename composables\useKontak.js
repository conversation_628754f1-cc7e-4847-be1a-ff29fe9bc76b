// Composable untuk halaman kontak menggunakan JSON structure yang siap migrasi ke Strapi
export const useKontak = () => {
  // State management
  const kontakData = ref(null)
  const loading = ref(true)
  const error = ref(null)

  // Fetch contact configuration dari <PERSON> atau Strapi
  const fetchKontakData = async () => {
    loading.value = true
    error.value = null
    
    try {
      // Load from JSON file directly
      const response = await import('~/data/kontak.json')
      kontakData.value = response.default || response
      console.log('Contact data loaded:', kontakData.value)
      
    } catch (err) {
      error.value = err
      console.error('Error loading contact data:', err)
      // Set fallback data jika error
      kontakData.value = null
    } finally {
      loading.value = false
    }
  }

  // Format address untuk display
  const getFormattedAddress = () => {
    if (!kontakData.value?.contactInfo?.address) return null
    
    const addr = kontakData.value.contactInfo.address
    return {
      full: `${addr.street}, ${addr.district}, ${addr.subDistrict}, ${addr.city} ${addr.postalCode}`,
      lines: [
        addr.street,
        addr.district,
        addr.subDistrict,
        `${addr.city} ${addr.postalCode}`
      ],
      coordinates: addr.coordinates
    }
  }

  // Get primary contact info
  const getPrimaryContact = () => {
    if (!kontakData.value?.contactInfo?.contact) return null
    
    const contact = kontakData.value.contactInfo.contact
    const primaryPhone = contact.phones.find(phone => phone.isPrimary)
    const primaryEmail = contact.emails.find(email => email.isPrimary)
    
    return {
      phone: primaryPhone,
      email: primaryEmail,
      businessHours: kontakData.value.contactInfo.businessHours
    }
  }

  // Get active social media links
  const getActiveSocialMedia = () => {
    if (!kontakData.value?.socialMedia) return []
    
    return kontakData.value.socialMedia.filter(social => social.isActive)
  }

  // Get FAQ by category
  const getFaqByCategory = (category = null) => {
    if (!kontakData.value?.faq?.items) return []
    
    const items = kontakData.value.faq.items.filter(item => item.isActive)
    
    if (category) {
      return items.filter(item => item.category === category)
    }
    
    return items
  }

  // Get FAQ categories
  const getFaqCategories = () => {
    if (!kontakData.value?.faq?.items) return []
    
    const categories = [...new Set(
      kontakData.value.faq.items
        .filter(item => item.isActive)
        .map(item => item.category)
    )]
    
    return categories.map(category => ({
      value: category,
      label: getCategoryLabel(category),
      count: kontakData.value.faq.items.filter(item => 
        item.category === category && item.isActive
      ).length
    }))
  }

  // Get category label
  const getCategoryLabel = (category) => {
    const labels = {
      recruitment: 'Recruitment',
      services: 'Layanan',
      general: 'Umum',
      partnership: 'Kemitraan'
    }
    return labels[category] || category
  }

  // Generate WhatsApp URL untuk kontak
  const generateWhatsAppUrl = (message = null) => {
    const primaryContact = getPrimaryContact()
    if (!primaryContact?.phone?.number) return null
    
    const phoneNumber = primaryContact.phone.number.replace(/[^0-9]/g, '')
    const defaultMessage = `Halo, saya ingin bertanya mengenai layanan Tata Karya Gemilang. PT. Terima kasih!`
    const whatsappMessage = message || defaultMessage
    
    return `https://wa.me/${phoneNumber}?text=${encodeURIComponent(whatsappMessage)}`
  }

  // Generate email URL
  const generateEmailUrl = (subject = null) => {
    const primaryContact = getPrimaryContact()
    if (!primaryContact?.email?.email) return null
    
    const defaultSubject = 'Pertanyaan mengenai layanan Tata Karya Gemilang'
    const emailSubject = subject || defaultSubject
    
    return `mailto:${primaryContact.email.email}?subject=${encodeURIComponent(emailSubject)}`
  }

  // Submit contact form (placeholder untuk integrasi Strapi)
  const submitContactForm = async (formData) => {
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // In production, this would send to Strapi API
    /*
    const response = await $fetch('/api/strapi/contact-form-submissions', {
      method: 'POST',
      body: {
        data: {
          ...formData,
          submittedAt: new Date().toISOString()
        }
      }
    })
    return response
    */
    
    return {
      success: true,
      message: kontakData.value?.contactForm?.successMessage || 'Pesan berhasil dikirim'
    }
  }

  // Get form configuration
  const getFormConfig = () => {
    return kontakData.value?.contactForm || null
  }

  // Get map configuration
  const getMapConfig = () => {
    return kontakData.value?.map || null
  }

  // Get CTA configuration
  const getCtaConfig = () => {
    return kontakData.value?.cta || null
  }

  // Get SEO configuration
  const getSeoConfig = () => {
    return kontakData.value?.seo || null
  }

  // Generate structured data untuk SEO
  const generateStructuredData = () => {
    if (!kontakData.value?.schema?.organization) return null
    
    const org = kontakData.value.schema.organization
    const contact = getPrimaryContact()
    
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: org.name,
      legalName: org.legalName,
      url: org.url,
      logo: org.logo,
      foundingDate: org.foundingDate,
      address: {
        '@type': 'PostalAddress',
        streetAddress: org.address.streetAddress,
        addressLocality: org.address.addressLocality,
        addressRegion: org.address.addressRegion,
        postalCode: org.address.postalCode,
        addressCountry: org.address.addressCountry
      },
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: contact?.phone?.number || org.contactPoint.telephone,
        contactType: org.contactPoint.contactType,
        availableLanguage: org.contactPoint.availableLanguage
      },
      sameAs: org.sameAs
    }
  }

  // Generate ContactPage structured data
  const generateContactPageSchema = () => {
    if (!kontakData.value) return null
    
    const contact = getPrimaryContact()
    const address = getFormattedAddress()
    
    return {
      '@context': 'https://schema.org',
      '@type': 'ContactPage',
      name: 'Kontak - Tata Karya Gemilang. PT',
      description: kontakData.value.seo?.description,
      url: kontakData.value.seo?.canonicalUrl,
      mainEntity: {
        '@type': 'Organization',
        name: kontakData.value.contactInfo.company.name,
        telephone: contact?.phone?.number,
        email: contact?.email?.email,
        address: {
          '@type': 'PostalAddress',
          streetAddress: address?.lines[0],
          addressLocality: kontakData.value.contactInfo.address.city,
          addressRegion: kontakData.value.contactInfo.address.city,
          postalCode: kontakData.value.contactInfo.address.postalCode,
          addressCountry: 'ID'
        },
        geo: {
          '@type': 'GeoCoordinates',
          latitude: address?.coordinates?.latitude,
          longitude: address?.coordinates?.longitude
        },
        openingHours: kontakData.value.contactInfo.businessHours.weekdays + ' ' + kontakData.value.contactInfo.businessHours.hours
      }
    }
  }

  // Initialize data immediately when composable is created
  fetchKontakData()

  return {
    // State
    kontakData: readonly(kontakData),
    loading: readonly(loading),
    error: readonly(error),
    
    // Core methods
    fetchKontakData,
    
    // Data getters
    getFormattedAddress,
    getPrimaryContact,
    getActiveSocialMedia,
    getFaqByCategory,
    getFaqCategories,
    getFormConfig,
    getMapConfig,
    getCtaConfig,
    getSeoConfig,
    
    // Utility methods
    generateWhatsAppUrl,
    generateEmailUrl,
    submitContactForm,
    generateStructuredData,
    generateContactPageSchema,
    getCategoryLabel,
    
    // Computed
    isReady: computed(() => !!kontakData.value && !loading.value),
    companyInfo: computed(() => kontakData.value?.contactInfo?.company || null),
    metadata: computed(() => kontakData.value?.metadata || null)
  }
} 