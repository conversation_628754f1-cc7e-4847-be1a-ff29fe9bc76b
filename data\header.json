{"logo": {"src": "/logo-gemilang-loker.webp", "alt": "Logo", "link": "/", "classes": "h-16 w-auto"}, "navigation": {"desktop": {"items": [{"id": "beranda", "text": "Be<PERSON><PERSON>", "link": "/", "style": "text-gray-700 hover:text-blue-600 font-medium transition-colors"}, {"id": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>", "link": "/lowongan", "style": "text-gray-700 hover:text-blue-600 font-medium transition-colors"}, {"id": "blog", "text": "Blog", "link": "/blog", "style": "text-gray-700 hover:text-blue-600 font-medium transition-colors"}, {"id": "tentang", "text": "Tentang", "link": "/tentang", "style": "text-gray-700 hover:text-blue-600 font-medium transition-colors"}, {"id": "kontak", "text": "Kontak", "link": "/kontak", "style": "text-gray-700 hover:text-blue-600 font-medium transition-colors"}]}, "mobile": {"items": [{"id": "beranda", "text": "Be<PERSON><PERSON>", "link": "/", "style": "block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"}, {"id": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>", "link": "/lowongan", "style": "block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"}, {"id": "blog", "text": "Blog", "link": "/blog", "style": "block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"}, {"id": "tentang", "text": "Tentang", "link": "/tentang", "style": "block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"}, {"id": "kontak", "text": "Kontak", "link": "/kontak", "style": "block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"}]}}, "styling": {"header": "bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50", "container": "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", "flexContainer": "flex justify-between items-center py-4", "desktopNav": "hidden md:flex space-x-8", "mobileButton": "md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors", "mobileMenu": "md:hidden border-t border-gray-200 py-4 space-y-2"}, "icons": {"hamburger": {"viewBox": "0 0 24 24", "path": "M4 6h16M4 12h16M4 18h16", "strokeWidth": "2"}, "close": {"viewBox": "0 0 24 24", "path": "M6 18L18 6M6 6l12 12", "strokeWidth": "2"}}, "seo": {"mobileMenuAriaLabel": "Toggle mobile menu"}}