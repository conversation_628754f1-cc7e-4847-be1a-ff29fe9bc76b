// TypeScript interfaces untuk Job data
// Ini akan memastikan konsistensi data antara local dan Strapi

export interface Job {
  id: number
  judul: string
  provinsi: string
  kabupaten: string
  deadline: string
  jenis: 'Full-time' | 'Part-time' | 'Kontrak'
  divisi: string
  slug: string
  deskripsi: string
  persyaratan: string[]
  gaji: string
  pendidikan: string
  jenisKelamin: string
  // Fields yang akan ditambahkan di Strapi
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
  featured?: boolean
  status?: 'active' | 'inactive' | 'expired'
  applicationCount?: number
  viewCount?: number
  companyLogo?: string
  benefits?: string[]
  workLocation?: 'onsite' | 'remote' | 'hybrid'
  experienceLevel?: 'entry' | 'mid' | 'senior'
  salaryMin?: number
  salaryMax?: number
  currency?: string
}

export interface JobFilters {
  provinsi?: string
  kabupaten?: string
  pendidikan?: string
  jenisKelamin?: string
  divisi?: string
  jenis?: string
  featured?: boolean
  status?: string
  experienceLevel?: string
  workLocation?: string
  salaryMin?: number
  salaryMax?: number
  search?: string
  page?: number
  pageSize?: number
  sort?: string
}

export interface JobResponse {
  data: Job[]
  meta: {
    total: number
    page: number
    pageSize: number
    pageCount: number
  }
}

export interface SingleJobResponse {
  data: Job
}

export interface FilterOptions {
  provinsi: string[]
  kabupaten: string[]
  pendidikan: string[]
  jenisKelamin: string[]
  divisi: string[]
  jenis: string[]
}

export interface JobStats {
  totalJobs: number
  totalProvinsi: number
  totalKabupaten: number
  totalPendidikan: number
  totalDivisi: number
  byJenis: {
    fullTime: number
    contract: number
    partTime: number
  }
}