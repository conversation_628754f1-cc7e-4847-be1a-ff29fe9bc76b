<template>
  <div class="card p-6 hover:scale-[1.02] transition-transform duration-200">
    <div class="flex justify-between items-start mb-4">
      <div class="flex items-start gap-4">
        <!-- Company Logo Image -->
        <div class="w-24 h-24 flex-shrink-0">
          <img
            :src="gambar || '/Astra_International-Logo.wine.png'"
            :alt="`Logo ${judul}`"
            class="w-full h-full object-contain rounded-md"
          />
        </div>
        
        <div class="flex-1">
          <h3 class="text-xl font-semibold text-gray-900 mb-1">{{ judul }}</h3>
          <div class="text-sm text-gray-500 mb-2">
            <span>Deadline: {{ formatDate(deadline) }}</span>
          </div>
          <div class="flex items-center text-gray-600 mb-2">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <span>{{ provinsi }}, {{ kabupaten }}</span>
          </div>
          <div class="flex flex-wrap gap-2 mb-3">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998a12.078 12.078 0 01.665-6.479L12 14z"/>
              </svg>
              {{ pendidikan }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              {{ jenisKelamin }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              {{ divisi }}
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="flex items-center justify-end">
      <NuxtLink :to="`/lowongan/${slug}`" class="btn-primary">
        Cek Loker
      </NuxtLink>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  judul: String,
  provinsi: String,
  kabupaten: String,
  deadline: String,
  jenis: String,
  divisi: String,
  slug: String,
  pendidikan: String,
  jenisKelamin: String,
  gambar: String
})

const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' }
  return new Date(dateString).toLocaleDateString('id-ID', options)
}
</script>