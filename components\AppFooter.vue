<template>
  <footer class="bg-gray-900 text-white py-8 sm:py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8">
        <!-- Company Section -->
        <div class="sm:col-span-2 md:col-span-1">
          <h3 class="text-lg font-semibold mb-4">{{ company.name }}</h3>
          <p class="text-gray-400 mb-4 text-sm sm:text-base">{{ company.description }}</p>
          <div class="flex space-x-4">
            <a
              v-for="social in socialMedia"
              :key="social.id"
              :href="social.link"
              class="text-gray-400 hover:text-white transition-colors"
              :title="social.name"
            >
              <svg class="w-5 h-5" fill="currentColor" :viewBox="social.icon.viewBox">
                <path :d="social.icon.path"/>
              </svg>
            </a>
          </div>
        </div>
        
        <!-- Services Section -->
        <div v-if="servicesSection">
          <h4 class="font-semibold mb-4">{{ servicesSection.title }}</h4>
          <ul class="space-y-2 text-gray-400 text-sm">
            <li v-for="service in servicesSection.items" :key="service.text">
              <a :href="service.link" class="hover:text-white transition-colors">{{ service.text }}</a>
            </li>
          </ul>
        </div>
        
        <!-- Quick Links Section -->
        <div v-if="quickLinksSection">
          <h4 class="font-semibold mb-4">{{ quickLinksSection.title }}</h4>
          <ul class="space-y-2 text-gray-400 text-sm">
            <li v-for="link in quickLinksSection.items" :key="link.text">
              <NuxtLink :to="link.link" class="hover:text-white transition-colors">{{ link.text }}</NuxtLink>
            </li>
          </ul>
        </div>
        
        <!-- Contact Section -->
        <div>
          <h4 class="font-semibold mb-4">{{ contact.title }}</h4>
          <div class="space-y-2 text-gray-400 text-sm">
            <p v-if="contact.email">Email: {{ contact.email }}</p>
            <p v-if="contact.phone">Telepon: {{ contact.phone }}</p>
            <p v-if="contact.address">Alamat: {{ contact.address }}</p>
          </div>
        </div>
      </div>
      
      <!-- Copyright -->
      <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400 text-sm">
        <p>{{ getFormattedCopyright() }}</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
// Use footer composable for data management
const {
  footerData,
  fetchFooterData,
  getCompanyData,
  getSocialMediaLinks,
  getFooterSections,
  getContactInfo,
  getFormattedCopyright
} = useFooter()

// Load footer data on component mount
onMounted(async () => {
  await fetchFooterData()
})

// Computed properties for template data
const company = computed(() => getCompanyData())
const socialMedia = computed(() => getSocialMediaLinks())
const contact = computed(() => getContactInfo())

// Get specific sections from footer data
const servicesSection = computed(() => {
  const sections = getFooterSections()
  return sections.find(section => section.id === 'services')
})

const quickLinksSection = computed(() => {
  const sections = getFooterSections()
  return sections.find(section => section.id === 'quicklinks')
})
</script>


