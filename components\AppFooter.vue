<template>
  <footer :class="styling.footer">
    <div :class="styling.container">
      <div :class="styling.grid">
        <!-- Company Section -->
        <div :class="styling.companySection">
          <h3 :class="`text-lg ${styling.sectionTitle}`">{{ company.name }}</h3>
          <p class="text-gray-400 mb-4 text-sm sm:text-base">{{ company.description }}</p>
          
          <!-- Social Media Links -->
          <div :class="styling.socialIcons">
            <a 
              v-for="social in socialMediaLinks"
              :key="social.id"
              :href="social.link"
              :class="styling.socialIcon"
              :title="social.name"
              target="_blank"
              rel="noopener noreferrer"
            >
              <svg class="w-5 h-5" fill="currentColor" :viewBox="social.icon.viewBox">
                <path :d="social.icon.path"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Dynamic Footer Sections -->
        <div v-for="section in footerSections" :key="section.id">
          <h4 :class="styling.sectionTitle">{{ section.title }}</h4>
          <ul :class="styling.sectionList">
            <li v-for="item in section.items" :key="item.text">
              <!-- External Links -->
              <a 
                v-if="isExternalLink(item)"
                :href="item.link"
                :class="styling.linkHover"
                target="_blank"
                rel="noopener noreferrer"
              >
                {{ item.text }}
              </a>
              
              <!-- Internal Links -->
              <NuxtLink 
                v-else
                :to="item.link"
                :class="styling.linkHover"
              >
                {{ item.text }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Contact Section -->
        <div>
          <h4 :class="styling.sectionTitle">{{ contact.title }}</h4>
          <div :class="styling.sectionList">
            <p>Email: {{ contact.email }}</p>
            <p v-if="contact.phone">Telepon: {{ contact.phone }}</p>
            <p>Alamat: {{ contact.address }}</p>
          </div>
        </div>
      </div>
      
      <!-- Copyright -->
      <div :class="styling.copyright">
        <p>{{ getFormattedCopyright() }}</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
// Composables
const { 
  fetchFooterData, 
  getCompanyData, 
  getSocialMediaLinks, 
  getFooterSections, 
  getContactInfo, 
  getStyling,
  isExternalLink,
  getFormattedCopyright
} = useFooter()

// Load footer data on component mount
await fetchFooterData()

// Computed properties for template
const company = computed(() => getCompanyData())
const socialMediaLinks = computed(() => getSocialMediaLinks())
const footerSections = computed(() => getFooterSections())
const contact = computed(() => getContactInfo())
const styling = computed(() => getStyling())
</script>
