<template>
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <NuxtLink to="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
            Beranda
          </NuxtLink>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <NuxtLink to="/lowongan" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors md:ml-2">
              Lowongan
            </NuxtLink>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <NuxtLink :to="`/lokasi/${encodeURIComponent(job.provinsi)}`" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors md:ml-2">
              {{ job.provinsi }}
            </NuxtLink>
          </div>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ job.judul }}</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Job Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
      <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
        <div class="flex-1">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ job.judul }}</h1>
          
          <div class="flex flex-wrap gap-4 mb-6">
            <div class="flex items-center text-gray-600">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              <NuxtLink 
                :to="`/lokasi/${encodeURIComponent(job.provinsi)}/${encodeURIComponent(job.kabupaten)}`"
                class="hover:text-blue-600 transition-colors"
              >
                {{ job.provinsi }}, {{ job.kabupaten }}
              </NuxtLink>
            </div>
            
            <div class="flex items-center text-gray-600">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span>{{ job.jenis }}</span>
            </div>
            
            <div class="flex items-center text-gray-600">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h1a1 1 0 011 1v5m-4 0h4"/>
              </svg>
              <span>{{ job.divisi }}</span>
            </div>
          </div>

          <div class="flex flex-wrap gap-2 mb-6">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
              </svg>
              {{ job.pendidikan }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              {{ job.jenisKelamin }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
              {{ job.divisi }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
              Deadline: {{ formatDate(job.deadline) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-8">
        <!-- Job Description -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Deskripsi Pekerjaan</h2>
          <p class="text-gray-700 leading-relaxed">{{ job.deskripsi }}</p>
        </div>

        <!-- Requirements -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Persyaratan</h2>
          <ul class="space-y-3">
            <li v-for="(requirement, index) in job.persyaratan" :key="index" class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              <span class="text-gray-700">{{ requirement }}</span>
            </li>
          </ul>
        </div>

        <!-- Apply Button Below Requirements -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Tertarik dengan posisi ini?</h2>
          <p class="text-sm text-gray-500 mb-2">
            Lamar melalui WhatsApp untuk respons cepat
          </p>
          <p class="text-xs text-blue-600 mb-3">
            Anda akan terhubung dengan: {{ currentWhatsAppConfig.name }}
          </p>
          <button
            @click="handleApplyViaWhatsApp"
            class="btn-primary w-full md:w-auto text-lg px-8 py-3 inline-flex items-center justify-center mx-auto"
          >
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.106"/>
            </svg>
            Info loker
          </button>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Job Info -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Lowongan</h3>
          <div class="space-y-4">
            <div>
              <div class="text-sm font-medium text-gray-500 mb-1">Lokasi</div>
              <div class="text-gray-900">
                <NuxtLink
                  :to="`/lokasi/${encodeURIComponent(job.provinsi)}/${encodeURIComponent(job.kabupaten)}`"
                  class="hover:text-blue-600 transition-colors"
                >
                  {{ job.kabupaten }}, {{ job.provinsi }}
                </NuxtLink>
              </div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-500 mb-1">Jenis Pekerjaan</div>
              <div class="text-gray-900">{{ job.jenis }}</div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-500 mb-1">Divisi</div>
              <div class="text-gray-900">{{ job.divisi }}</div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-500 mb-1">Pendidikan</div>
              <div class="text-gray-900">{{ job.pendidikan }}</div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-500 mb-1">Jenis Kelamin</div>
              <div class="text-gray-900">{{ job.jenisKelamin }}</div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-500 mb-1">Batas Lamaran</div>
              <div class="text-gray-900">{{ formatDate(job.deadline) }}</div>
            </div>
          </div>
        </div>

        <!-- Apply Button in Sidebar -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Tertarik dengan posisi ini?</h3>
          <a 
            href="https://recruitment.gemilanghebat.com/"
            target="_blank"
            rel="noopener noreferrer"
            class="btn-primary w-full text-center inline-flex items-center justify-center mb-3"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Lamar Pekerjaan
          </a>
          <p class="text-sm text-gray-500 text-center">
            Anda akan diarahkan ke portal recruitment PT Gemilang
          </p>
        </div>

        <!-- Share -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Bagikan Lowongan</h3>
          <button @click="handleShareJob" class="btn-secondary w-full text-center">
            <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.106"/>
            </svg>
            Bagikan via WhatsApp
          </button>
        </div>

        <!-- Related Jobs -->
        <div v-if="relatedJobs.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Lowongan Serupa</h3>
          <div class="space-y-4">
            <div v-for="relatedJob in relatedJobs" :key="relatedJob.id" class="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
              <NuxtLink :to="`/lowongan/${relatedJob.slug}`" class="block hover:text-blue-600 transition-colors">
                <h4 class="font-medium text-gray-900 mb-1">{{ relatedJob.judul }}</h4>
                <p class="text-sm text-gray-500">{{ relatedJob.kabupaten }}, {{ relatedJob.provinsi }}</p>
                <p class="text-sm text-gray-400">{{ relatedJob.divisi }}</p>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Get route params
const route = useRoute()
const slug = route.params.slug

// Import data
const { default: jobsData } = await import('~/data/data.json')

// Find job by slug
const job = jobsData.find(job => job.slug === slug)



// Handle 404 if job not found
if (!job) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Lowongan tidak ditemukan'
  })
}

// Use WhatsApp composable for dynamic WhatsApp handling
const { getLocationWhatsApp, applyViaWhatsApp, shareViaWhatsApp } = useWhatsApp()

// Get WhatsApp config for current job location
const currentWhatsAppConfig = computed(() => {
  return getLocationWhatsApp(job.provinsi, job.kabupaten)
})

// Find related jobs (same province or division, exclude current job)
const relatedJobs = computed(() => {
  return jobsData
    .filter(j => j.id !== job.id && (j.provinsi === job.provinsi || j.divisi === job.divisi))
    .slice(0, 3)
})

// Methods
const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' }
  return new Date(dateString).toLocaleDateString('id-ID', options)
}

// Handler functions using the composable
const handleApplyViaWhatsApp = () => {
  applyViaWhatsApp(job)
}

const handleShareJob = () => {
  shareViaWhatsApp(job)
}

// SEO
useHead({
  title: `${job.judul} - ${job.kabupaten}, ${job.provinsi} | JobPortal`,
  meta: [
    {
      name: 'description',
      content: `Lowongan kerja ${job.judul} di ${job.kabupaten}, ${job.provinsi}. ${job.jenis} - ${job.divisi}. Deadline: ${formatDate(job.deadline)}`
    },
    {
      property: 'og:title',
      content: `${job.judul} - JobPortal`
    },
    {
      property: 'og:description',
      content: job.deskripsi.substring(0, 160)
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ]
})
</script>