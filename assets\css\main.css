@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply font-sans antialiased;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 sm:px-6 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
  }
  
  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium px-4 sm:px-6 py-2 rounded-lg transition-all duration-200 border border-gray-300;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200;
  }
  
  .input-field {
    @apply w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  /* Mobile-specific improvements */
  @media (max-width: 640px) {
    .btn-primary, .btn-secondary {
      @apply text-sm px-4 py-2;
    }
    
    .card {
      @apply mx-2;
    }
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Prevent horizontal scroll on mobile */
body {
  overflow-x: hidden;
}

/* Mobile menu animation */
@media (max-width: 768px) {
  .mobile-menu-enter-active,
  .mobile-menu-leave-active {
    transition: all 0.3s ease;
  }
  
  .mobile-menu-enter-from,
  .mobile-menu-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }
}