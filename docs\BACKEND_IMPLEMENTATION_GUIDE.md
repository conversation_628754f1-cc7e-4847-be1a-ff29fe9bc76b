# 🚀 Backend Implementation Guide - Strapi Integration

## 📋 **OVERVIEW**

Panduan lengkap untuk backend team dalam mengimplementasikan Strapi CMS untuk job portal. Aplikasi frontend sudah 95% siap, hanya perlu setup backend dan minor updates.

---

## 🎯 **STRAPI CONTENT TYPE SCHEMA**

### **Job Content Type**

Buat content type dengan nama `job` (singular) / `jobs` (plural):

```json
{
  "kind": "collectionType",
  "collectionName": "jobs",
  "info": {
    "singularName": "job",
    "pluralName": "jobs",
    "displayName": "Job",
    "description": "Job listings for the portal"
  },
  "options": {
    "draftAndPublish": true
  },
  "pluginOptions": {},
  "attributes": {
    "judul": {
      "type": "string",
      "required": true,
      "maxLength": 255
    },
    "provinsi": {
      "type": "string",
      "required": true,
      "maxLength": 100
    },
    "kabupaten": {
      "type": "string",
      "required": true,
      "maxLength": 100
    },
    "deadline": {
      "type": "date",
      "required": true
    },
    "jenis": {
      "type": "enumeration",
      "enum": ["Full-time", "Part-time", "Kontrak"],
      "required": true
    },
    "divisi": {
      "type": "string",
      "required": true,
      "maxLength": 100
    },
    "slug": {
      "type": "uid",
      "targetField": "judul",
      "required": true
    },
    "gambar": {
      "type": "media",
      "multiple": false,
      "required": false,
      "allowedTypes": ["images"]
    },
    "deskripsi": {
      "type": "text",
      "required": true
    },
    "persyaratan": {
      "type": "json",
      "required": true
    },
    "gaji": {
      "type": "string",
      "required": true,
      "maxLength": 100
    },
    "pendidikan": {
      "type": "string",
      "required": true,
      "maxLength": 50
    },
    "jenisKelamin": {
      "type": "string",
      "required": true,
      "maxLength": 50
    },
    "featured": {
      "type": "boolean",
      "default": false
    },
    "status": {
      "type": "enumeration",
      "enum": ["active", "inactive", "expired"],
      "default": "active"
    },
    "applicationCount": {
      "type": "integer",
      "default": 0
    },
    "viewCount": {
      "type": "integer",
      "default": 0
    },
    "benefits": {
      "type": "json",
      "required": false
    },
    "workLocation": {
      "type": "enumeration",
      "enum": ["onsite", "remote", "hybrid"],
      "default": "onsite"
    },
    "experienceLevel": {
      "type": "enumeration",
      "enum": ["entry", "mid", "senior"],
      "default": "entry"
    },
    "salaryMin": {
      "type": "integer",
      "required": false
    },
    "salaryMax": {
      "type": "integer",
      "required": false
    },
    "currency": {
      "type": "string",
      "default": "IDR",
      "maxLength": 10
    }
  }
}
```

---

## 🔧 **STRAPI SETUP STEPS**

### **1. Installation**

```bash
# Create new Strapi project
npx create-strapi-app@latest strapi-jobportal --quickstart

# Or with custom database
npx create-strapi-app@latest strapi-jobportal
```

### **2. Database Configuration**

**Recommended: PostgreSQL**

```javascript
// config/database.js
module.exports = ({ env }) => ({
  connection: {
    client: 'postgres',
    connection: {
      host: env('DATABASE_HOST', 'localhost'),
      port: env.int('DATABASE_PORT', 5432),
      database: env('DATABASE_NAME', 'strapi_jobportal'),
      user: env('DATABASE_USERNAME', 'strapi'),
      password: env('DATABASE_PASSWORD', ''),
      ssl: env.bool('DATABASE_SSL', false),
    },
  },
});
```

### **3. Environment Variables**

```bash
# .env
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=strapi_jobportal
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=your-password

APP_KEYS=your-app-keys-here
API_TOKEN_SALT=your-api-token-salt
ADMIN_JWT_SECRET=your-admin-jwt-secret
TRANSFER_TOKEN_SALT=your-transfer-token-salt
JWT_SECRET=your-jwt-secret
```

### **4. CORS Configuration**

```javascript
// config/middlewares.js
module.exports = [
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'https:'],
          'img-src': ["'self'", 'data:', 'blob:', 'https:'],
          'media-src': ["'self'", 'data:', 'blob:', 'https:'],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  {
    name: 'strapi::cors',
    config: {
      enabled: true,
      headers: '*',
      origin: [
        'http://localhost:3000',
        'https://your-frontend-domain.com'
      ]
    }
  },
  'strapi::poweredBy',
  'strapi::logger',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
```

---

## 📊 **DATA MIGRATION**

### **1. Prepare Migration Script**

File `scripts/migrate-to-strapi.js` sudah tersedia di frontend. Copy dan jalankan:

```bash
# Set environment variables
export STRAPI_URL=http://localhost:1337
export STRAPI_TOKEN=your-api-token

# Run migration
node scripts/migrate-to-strapi.js
```

### **2. Sample Data Structure**

Data yang akan di-migrate:

```json
{
  "id": 1,
  "judul": "Staff Administrasi",
  "provinsi": "Jawa Tengah",
  "kabupaten": "Klaten",
  "deadline": "2025-06-30",
  "jenis": "Full-time",
  "divisi": "Administrasi",
  "slug": "staff-administrasi-klaten",
  "gambar": "/Toyota_logo_(Red).svg.png",
  "deskripsi": "Dicari staff administrasi...",
  "persyaratan": [
    "Minimal lulusan SMA/SMK",
    "Menguasai Microsoft Office"
  ],
  "gaji": "Rp 3.500.000 - 4.500.000",
  "pendidikan": "SMA/SMK",
  "jenisKelamin": "Laki-laki/Perempuan"
}
```

---

## 🔐 **API PERMISSIONS**

### **Required Permissions for Public Role:**

1. **Job Collection:**
   - ✅ `find` - List all jobs
   - ✅ `findOne` - Get single job
   - ❌ `create` - Only admin
   - ❌ `update` - Only admin  
   - ❌ `delete` - Only admin

### **API Token Setup:**

1. Go to Settings > API Tokens
2. Create new token with name "Frontend API"
3. Set type to "Read-only"
4. Copy token untuk frontend configuration

---

## 🧪 **TESTING ENDPOINTS**

### **Test API Responses:**

```bash
# Get all jobs
curl "http://localhost:1337/api/jobs?populate=*"

# Get jobs with filters
curl "http://localhost:1337/api/jobs?populate=*&filters[provinsi][\$eq]=DKI Jakarta"

# Get single job by slug
curl "http://localhost:1337/api/jobs?populate=*&filters[slug][\$eq]=web-developer-jakarta-selatan"

# Get jobs with pagination
curl "http://localhost:1337/api/jobs?populate=*&pagination[page]=1&pagination[pageSize]=10"
```

### **Expected Response Format:**

```json
{
  "data": [
    {
      "id": 1,
      "attributes": {
        "judul": "Staff Administrasi",
        "provinsi": "Jawa Tengah",
        "kabupaten": "Klaten",
        "deadline": "2025-06-30",
        "jenis": "Full-time",
        "divisi": "Administrasi",
        "slug": "staff-administrasi-klaten",
        "deskripsi": "Dicari staff administrasi...",
        "persyaratan": ["Minimal lulusan SMA/SMK"],
        "gaji": "Rp 3.500.000 - 4.500.000",
        "pendidikan": "SMA/SMK",
        "jenisKelamin": "Laki-laki/Perempuan",
        "featured": false,
        "status": "active",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "publishedAt": "2024-01-01T00:00:00.000Z",
        "gambar": {
          "data": {
            "id": 1,
            "attributes": {
              "url": "/uploads/toyota_logo.png"
            }
          }
        }
      }
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 10
    }
  }
}
```

---

## 🚀 **DEPLOYMENT CHECKLIST**

### **Production Environment:**

1. **Database Setup:**
   - [ ] Setup PostgreSQL database
   - [ ] Configure connection strings
   - [ ] Run migrations

2. **Strapi Configuration:**
   - [ ] Set production environment variables
   - [ ] Configure CORS for production domain
   - [ ] Setup SSL certificates
   - [ ] Configure file upload (AWS S3/Cloudinary)

3. **Security:**
   - [ ] Change default admin credentials
   - [ ] Setup API rate limiting
   - [ ] Configure security headers
   - [ ] Setup monitoring

4. **Performance:**
   - [ ] Enable caching
   - [ ] Configure CDN for media files
   - [ ] Setup database indexing
   - [ ] Configure compression

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### **Common Issues:**

1. **CORS Errors:**
   - Check `config/middlewares.js`
   - Verify frontend domain in allowed origins

2. **API Token Issues:**
   - Verify token permissions
   - Check token expiration
   - Ensure correct headers

3. **Database Connection:**
   - Verify database credentials
   - Check network connectivity
   - Ensure database exists

### **Monitoring:**

- Setup logging for API requests
- Monitor database performance
- Track API response times
- Setup error alerting

---

## 🎉 **NEXT STEPS**

1. **Setup Strapi instance**
2. **Create Job content type**
3. **Configure permissions**
4. **Run data migration**
5. **Test API endpoints**
6. **Coordinate with frontend team**
7. **Deploy to production**

**Estimated Time: 1-2 days**
**Complexity: Low-Medium**
**Risk: Very Low**
