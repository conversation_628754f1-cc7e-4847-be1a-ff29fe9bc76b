// API route untuk single job by slug

export default defineEventHandler(async (event) => {
  const slug = getRouterParam(event, 'slug')
  
  try {
    // Import data lokal
    const jobsData = await import('~/data/data.json').then(m => m.default)
    
    const job = jobsData.find(job => job.slug === slug)
    
    if (!job) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Job not found'
      })
    }
    
    return {
      data: job
    }
  } catch (error: any) {
    if (error?.statusCode === 404) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch job'
    })
  }
})