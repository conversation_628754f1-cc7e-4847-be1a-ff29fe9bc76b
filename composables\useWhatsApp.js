// Composable untuk mengelola WhatsApp berdasarkan lokasi
import { getWhatsAppConfig } from '~/config/whatsapp.js'

export const useWhatsApp = () => {
  
  // Fungsi untuk mendapatkan konfigurasi WhatsApp berdasarkan lokasi
  const getLocationWhatsApp = (provinsi, kabupaten) => {
    return getWhatsAppConfig(provinsi, kabupaten)
  }
  
  // Fungsi untuk membuat URL WhatsApp untuk melamar pekerjaan
  const createApplyWhatsAppUrl = (job) => {
    const config = getLocationWhatsApp(job.provinsi, job.kabupaten)
    
    const message = `Halo ${config.name},

Saya tertarik untuk melamar posisi *${job.judul}* di ${job.kabupaten}, ${job.provinsi}.

Detail lowongan:
- Posisi: ${job.judul}
- Lokasi: ${job.kabupaten}, ${job.provinsi}
- Divisi: ${job.divisi}
- Pendidikan: ${job.pendidikan}
- <PERSON><PERSON>: ${job.jenisKelamin}

Mohon informasi lebih lanjut mengenai proses recruitment untuk posisi ini.

Terima kasih.`

    return `https://wa.me/${config.number}?text=${encodeURIComponent(message)}`
  }
  
  // Fungsi untuk membuat URL WhatsApp untuk berbagi lowongan
  const createShareWhatsAppUrl = (job, url) => {
    const text = `Lowongan kerja: ${job.judul} di ${job.kabupaten}, ${job.provinsi}`
    return `https://wa.me/?text=${encodeURIComponent(text + ' - ' + url)}`
  }
  
  // Fungsi untuk membuka WhatsApp untuk melamar
  const applyViaWhatsApp = (job) => {
    const whatsappUrl = createApplyWhatsAppUrl(job)
    window.open(whatsappUrl, '_blank')
  }
  
  // Fungsi untuk berbagi lowongan via WhatsApp
  const shareViaWhatsApp = (job, url = null) => {
    const shareUrl = url || window.location.href
    const whatsappUrl = createShareWhatsAppUrl(job, shareUrl)
    window.open(whatsappUrl, '_blank')
  }
  
  return {
    getLocationWhatsApp,
    createApplyWhatsAppUrl,
    createShareWhatsAppUrl,
    applyViaWhatsApp,
    shareViaWhatsApp
  }
}
