// Konfigurasi WhatsApp berdasarkan lokasi
// Format: 'provinsi|kabupaten': { number, name }

export const whatsappConfig = {
  // Default/fallback admin
  default: {
    number: '6287777733566',
    name: 'Ad<PERSON> Recruitment Tata Karya Gemilang. PT'
  },
  
  // Mapping berdasarkan provinsi dan kabupaten
  locations: {
    // DKI Jakarta
    'DKI Jakarta|Jakarta Selatan': {
      number: '6287777733566',
      name: 'Admin Jakarta Selatan'
    },
    'DKI Jakarta|Jakarta Pusat': {
      number: '6287777733566', 
      name: '<PERSON>min Jakarta Pusat'
    },
    'DKI Jakarta|Jakarta Barat': {
      number: '6287777733566',
      name: 'Admin Jakarta Barat'
    },
    
    // Jawa Barat
    'Jawa Barat|Bandung': {
      number: '6287777733566',
      name: '<PERSON><PERSON>'
    },
    'Jawa Barat|Bogor': {
      number: '6287777733566',
      name: '<PERSON><PERSON>'
    },
    
    // Jawa Tengah
    'Jawa Tengah|Klaten': {
      number: '6287777733566',
      name: '<PERSON><PERSON>'
    },
    'Jawa Tengah|Semarang': {
      number: '6287777733566',
      name: 'Admin Semarang'
    },
    
    // D.I.Yogyakarta
    'D.I.Yogyakarta|Sleman': {
      number: '6287777733566',
      name: 'Admin Sleman'
    },
    'D.I.Yogyakarta|Yogyakarta': {
      number: '6287777733566',
      name: 'Admin Yogyakarta'
    },
    
    // Jawa Timur
    'Jawa Timur|Surabaya': {
      number: '6287777733566',
      name: 'Admin Surabaya'
    }
  },
  
  // Mapping berdasarkan provinsi saja (jika kabupaten tidak ditemukan)
  provinces: {
    'DKI Jakarta': {
      number: '6287777733566',
      name: 'Admin DKI Jakarta'
    },
    'Jawa Barat': {
      number: '6287777733566',
      name: 'Admin Jawa Barat'
    },
    'Jawa Tengah': {
      number: '6287777733566',
      name: 'Admin Jawa Tengah'
    },
    'D.I.Yogyakarta': {
      number: '6287777733566',
      name: 'Admin D.I.Yogyakarta'
    },
    'Jawa Timur': {
      number: '6287777733566',
      name: 'Admin Jawa Timur'
    }
  }
}

// Fungsi untuk mendapatkan konfigurasi WhatsApp berdasarkan lokasi
export const getWhatsAppConfig = (provinsi, kabupaten) => {
  // Coba cari berdasarkan provinsi|kabupaten
  const locationKey = `${provinsi}|${kabupaten}`
  if (whatsappConfig.locations[locationKey]) {
    return whatsappConfig.locations[locationKey]
  }
  
  // Jika tidak ditemukan, coba berdasarkan provinsi saja
  if (whatsappConfig.provinces[provinsi]) {
    return whatsappConfig.provinces[provinsi]
  }
  
  // Jika tidak ditemukan, gunakan default
  return whatsappConfig.default
}
