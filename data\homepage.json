{"hero": {"title": "<PERSON><PERSON><PERSON> <span class=\"text-yellow-300\"> <PERSON><PERSON>aik</span><br class=\"hidden sm:block\"> <PERSON><PERSON><PERSON> Gemilang", "subtitle": "Tempat mencari Info Lowongan kerja terbaik yang paling sesuai dengan minat dan kompetensi anda.", "buttons": [{"text": "<PERSON><PERSON><PERSON><PERSON>", "link": "/lowongan", "type": "primary", "style": "btn-primary bg-yellow-400 hover:bg-yellow-500 text-gray-900 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 w-full sm:w-auto", "icon": "search"}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "link": "#features", "type": "secondary", "style": "text-white border-2 border-white hover:bg-white hover:text-blue-700 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold transition-all duration-300 w-full sm:w-auto text-center"}], "floatingElements": [{"position": "top-20 left-4 sm:left-10", "size": "w-16 h-16 sm:w-20 sm:h-20", "color": "bg-yellow-300", "animation": "animate-pulse"}, {"position": "bottom-20 right-4 sm:right-10", "size": "w-24 h-24 sm:w-32 sm:h-32", "color": "bg-blue-300", "animation": "animate-bounce"}, {"position": "top-1/2 right-1/4", "size": "w-12 h-12 sm:w-16 sm:h-16", "color": "bg-indigo-300", "animation": "animate-pulse delay-1000"}]}, "stats": {"enabled": false, "title": "Statistik Platform", "items": [{"id": 1, "value": "dynamic", "label": "Lowongan Aktif", "color": "blue", "source": "totalJobs"}, {"id": 2, "value": "100+", "label": "Perusahaan Partner", "color": "green"}, {"id": 3, "value": "10K+", "label": "Kandi<PERSON><PERSON>", "color": "purple"}, {"id": 4, "value": "95%", "label": "Tingkat <PERSON>", "color": "orange"}]}, "jobCategories": {"title": "Kate<PERSON>i <span class=\"text-blue-600\"><PERSON><PERSON><PERSON><PERSON><PERSON></span>", "subtitle": "<PERSON><PERSON><PERSON>an kerja berdasarkan divisi dan kategori yang <PERSON>a minati", "note": "Data kategori diambil secara dinamis dari data lowongan kerja", "dynamicSource": "computed from jobs data", "maxCategories": 8, "linkPattern": "/kategori/{categoryName}"}, "features": {"enabled": false, "title": "Mengapa Memilih <span class=\"text-blue-600\">JobPortal</span>?", "subtitle": "<PERSON><PERSON> berkomitmen member<PERSON>n pengalaman terbaik dalam pencarian kerja dengan fitur-fitur unggulan", "items": [{"id": 1, "title": "<PERSON><PERSON><PERSON>", "description": "Sistem pencarian canggih dengan filter lokasi, pen<PERSON><PERSON><PERSON>, dan kriteria lainnya untuk menemukan pekerjaan yang tepat sesuai kualifikasi Anda.", "icon": "search", "color": "blue"}, {"id": 2, "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> dengan per<PERSON>-per<PERSON><PERSON><PERSON> terkemuka dan startup inovatif yang menawarkan lingkungan kerja terbaik.", "icon": "check-circle", "color": "green"}, {"id": 3, "title": "Proses Cepat", "description": "<PERSON> p<PERSON><PERSON><PERSON> dengan mudah dan dapatkan respons cepat dari perusahaan melalui platform yang user-friendly.", "icon": "lightning", "color": "purple"}]}, "howItWorks": {"title": "<PERSON> <span class=\"text-blue-600\">Mendapa<PERSON></span>", "subtitle": "Tiga langkah mudah untuk menemukan pekerjaan impian <PERSON>a", "steps": [{"id": 1, "number": "1", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Telusuri ribuan lowongan kerja dari berbagai industri dan lokasi sesuai dengan minat dan keahlian <PERSON>.", "color": "blue"}, {"id": 2, "number": "2", "title": "<PERSON>", "description": "<PERSON><PERSON> lamaran dengan mudah melalui platform kami. CV dan profil <PERSON>a akan langsung terkirim ke perusahaan.", "color": "green"}, {"id": 3, "number": "3", "title": "<PERSON><PERSON>", "description": "Dapatkan panggilan interview dan mulai perjalanan karir gemilang <PERSON>a bersa<PERSON> per<PERSON>an impian.", "color": "purple"}]}, "testimonials": {"title": "<PERSON><PERSON> Kata <span class=\"text-blue-600\"><PERSON><PERSON><PERSON></span>?", "subtitle": "Testimoni dari para profesional yang telah menemukan karir impian melalui <PERSON>", "items": [{"id": 1, "name": "<PERSON><PERSON>", "position": "Software Developer", "avatar": "AS", "avatarColor": "bg-blue-500", "rating": 5, "testimonial": "JobPortal membantu saya menemukan pekerjaan impian di perusahaan teknologi terkemuka. Prosesnya sangat mudah dan cepat!"}, {"id": 2, "name": "<PERSON><PERSON>", "position": "Marketing Manager", "avatar": "SP", "avatarColor": "bg-green-500", "rating": 5, "testimonial": "Platform yang sangat user-friendly. Dalam 2 minggu saya sudah mendapat 3 panggilan interview dari per<PERSON><PERSON><PERSON> ternama."}, {"id": 3, "name": "<PERSON><PERSON><PERSON>", "position": "Data Analyst", "avatar": "RH", "avatarColor": "bg-purple-500", "rating": 5, "testimonial": "Sebagai fresh graduate, JobPortal memberikan kesempatan yang luar biasa untuk memulai karir di bidang yang saya minati."}]}, "blog": {"title": "Tips Karir & Artikel Terbaru", "subtitle": "Dapatkan insight dan tips terbaru untuk mengembangkan karir Anda", "note": "Data blog diambil secara dinamis menggunakan useBlog() composable", "dynamicSource": "useBlog().getFeaturedPosts(3)", "viewAllLink": "/blog", "viewAllText": "<PERSON><PERSON>"}, "cta": {"title": "Siap <PERSON>?", "subtitle": "Bergabunglah dengan ribuan profesional yang telah menemukan pekerjaan impian mereka melalui JobPortal", "buttons": [{"text": "<PERSON><PERSON>", "link": "/lowongan", "type": "primary", "style": "btn-primary bg-yellow-400 hover:bg-yellow-500 text-gray-900 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 w-full sm:w-auto"}, {"text": "<PERSON><PERSON><PERSON>", "link": "https://recruitment.gemilanghebat.com/register", "type": "external", "style": "text-white border-2 border-white hover:bg-white hover:text-blue-700 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold transition-all duration-300 w-full sm:w-auto text-center", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}]}, "seo": {"title": "JobPortal - <PERSON><PERSON><PERSON>", "metaDescription": "Platform pencarian kerja terpercaya yang menghubungkan talenta terbaik dengan perusahaan impian. Wujudkan karir gemilang bersama ribuan peluang kerja berkualitas di seluruh Indonesia.", "keywords": "<PERSON><PERSON><PERSON> kerja, pen<PERSON><PERSON> kerja, karir, p<PERSON><PERSON><PERSON><PERSON>, job portal, indonesia, fresh graduate, profesional", "ogTitle": "JobPortal - <PERSON><PERSON><PERSON>", "ogDescription": "Platform pencarian kerja terpercaya dengan ribuan lowongan dari perusahaan terkemuka. <PERSON><PERSON> perjalanan karir gemilang Anda sekarang!", "ogType": "website", "pageMetaTitle": "JobPortal - Platform Pencarian Kerja Terpercaya"}}