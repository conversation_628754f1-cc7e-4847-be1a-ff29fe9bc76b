<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <NuxtLink to="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
            Beranda
          </NuxtLink>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ provinsi }}</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">
        Lowongan Kerja di {{ provinsi }}
      </h1>
      <p class="text-lg text-gray-600">
        Ditemukan {{ provinsiJobs.length }} lowongan kerja di {{ provinsi }}
      </p>
    </div>

    <!-- Province Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="text-2xl font-bold text-blue-600 mb-2">{{ provinsiJobs.length }}</div>
        <div class="text-gray-600">Total Lowongan</div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="text-2xl font-bold text-green-600 mb-2">{{ uniqueCities.length }}</div>
        <div class="text-gray-600">Kota/Kabupaten</div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="text-2xl font-bold text-orange-600 mb-2">{{ fullTimeCount }}</div>
        <div class="text-gray-600">Full-time</div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="text-2xl font-bold text-purple-600 mb-2">{{ uniqueDivisions.length }}</div>
        <div class="text-gray-600">Divisi</div>
      </div>
    </div>

    <!-- Cities in Province -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Kota/Kabupaten di {{ provinsi }}</h2>
      <div class="flex flex-wrap gap-2">
        <NuxtLink
          v-for="city in citiesInProvince"
          :key="city.name"
          :to="`/lokasi/${encodeURIComponent(provinsi)}/${encodeURIComponent(city.name)}`"
          class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors duration-200"
        >
          {{ city.name }}
          <span class="ml-2 bg-blue-200 text-blue-800 text-xs px-2 py-1 rounded-full">
            {{ city.count }}
          </span>
        </NuxtLink>
      </div>
    </div>

    <!-- Job Listings -->
    <div v-if="provinsiJobs.length > 0">
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">Semua Lowongan di {{ provinsi }}</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        <JobCard
          v-for="job in provinsiJobs"
          :key="job.id"
          :judul="job.judul"
          :provinsi="job.provinsi"
          :kabupaten="job.kabupaten"
          :deadline="job.deadline"
          :jenis="job.jenis"
          :divisi="job.divisi"
          :slug="job.slug"
          :pendidikan="job.pendidikan"
          :jenisKelamin="job.jenisKelamin"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-16">
      <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h1a1 1 0 011 1v5m-4 0h4"/>
      </svg>
      <h3 class="text-xl font-medium text-gray-900 mb-2">Belum ada lowongan</h3>
      <p class="text-gray-500 mb-6">Saat ini belum ada lowongan kerja di {{ provinsi }}</p>
      <NuxtLink to="/lowongan" class="btn-primary">
        Lihat Semua Lowongan
      </NuxtLink>
    </div>
  </div>
</template>

<script setup>
// Define page meta with validation
definePageMeta({
  validate: async (route) => {
    const provinsi = decodeURIComponent(route.params.provinsi)
    
    // Import data for validation
    const { default: jobsData } = await import('~/data/data.json')
    
    // Filter jobs by province
    const provinsiJobs = jobsData.filter(job => job.provinsi === provinsi)
    
    // If no jobs found, check if the province exists in our data
    if (provinsiJobs.length === 0) {
      const allProvinces = [...new Set(jobsData.map(job => job.provinsi))]
      
      if (!allProvinces.includes(provinsi)) {
        throw createError({
          statusCode: 404,
          statusMessage: `Provinsi ${provinsi} tidak ditemukan`
        })
      }
    }
    
    return true
  }
})

// Get route params
const route = useRoute()
const provinsi = decodeURIComponent(route.params.provinsi)

// Import data
const { default: jobsData } = await import('~/data/data.json')

// Filter jobs by province
const provinsiJobs = computed(() => {
  return jobsData.filter(job => job.provinsi === provinsi)
})

// Get unique cities in province with job counts
const citiesInProvince = computed(() => {
  const cityCount = {}
  
  provinsiJobs.value.forEach(job => {
    cityCount[job.kabupaten] = (cityCount[job.kabupaten] || 0) + 1
  })
  
  return Object.entries(cityCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
})

// Computed stats
const uniqueCities = computed(() => 
  [...new Set(provinsiJobs.value.map(job => job.kabupaten))]
)

const uniqueDivisions = computed(() => 
  [...new Set(provinsiJobs.value.map(job => job.divisi))]
)

const fullTimeCount = computed(() => 
  provinsiJobs.value.filter(job => job.jenis === 'Full-time').length
)

// SEO
useHead({
  title: `Lowongan Kerja di ${provinsi} - JobPortal`,
  link: [
    {
      rel: 'canonical',
      href: `https://lokergemilang.com/lokasi/${encodeURIComponent(provinsi)}`
    }
  ],
  meta: [
    {
      name: 'description',
      content: `Temukan ${provinsiJobs.value.length} lowongan kerja terbaru di ${provinsi}. Berbagai posisi dari perusahaan terpercaya di ${uniqueCities.value.length} kota/kabupaten.`
    }
  ]
})
</script>