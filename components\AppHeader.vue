<template>
  <header :class="styling.header">
    <div :class="styling.container">
      <div :class="styling.flexContainer">
        <!-- Logo -->
        <NuxtLink :to="logo.link">
          <img 
            :src="logo.src" 
            :alt="logo.alt" 
            :class="`${logo.height} ${logo.width}`"
          >
        </NuxtLink>
        
        <!-- Desktop Navigation -->
        <nav :class="styling.desktopNav">
          <NuxtLink
            v-for="item in desktopNavigation"
            :key="item.id"
            :to="item.link"
            :class="getNavigationClasses(item, false)"
          >
            {{ item.text }}
          </NuxtLink>
        </nav>

        <!-- Mobile Menu Button -->
        <button 
          @click="toggleMobileMenu"
          :class="styling.mobileButton"
          :aria-label="seoData.mobileMenuAriaLabel"
        >
          <svg 
            v-if="!isMobileMenuOpen" 
            class="w-6 h-6" 
            fill="none" 
            stroke="currentColor" 
            :viewBox="icons.hamburger.viewBox"
          >
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              :stroke-width="icons.hamburger.strokeWidth" 
              :d="icons.hamburger.path"
            />
          </svg>
          <svg 
            v-else 
            class="w-6 h-6" 
            fill="none" 
            stroke="currentColor" 
            :viewBox="icons.close.viewBox"
          >
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              :stroke-width="icons.close.strokeWidth" 
              :d="icons.close.path"
            />
          </svg>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <div
        v-show="isMobileMenuOpen"
        :class="styling.mobileMenu"
      >
        <NuxtLink
          v-for="item in mobileNavigation"
          :key="item.id"
          :to="item.link"
          @click="closeMobileMenu"
          :class="getNavigationClasses(item, true)"
        >
          {{ item.text }}
        </NuxtLink>
      </div>
    </div>
  </header>
</template>

<script setup>
// Composables
const { 
  fetchHeaderData, 
  getLogoData, 
  getDesktopNavigation, 
  getMobileNavigation, 
  getStyling, 
  getIcons, 
  getSEOData,
  getNavigationClasses 
} = useHeader()

// Mobile menu state
const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

// Load header data on component mount
await fetchHeaderData()

// Computed properties for template
const logo = computed(() => getLogoData())
const desktopNavigation = computed(() => getDesktopNavigation())
const mobileNavigation = computed(() => getMobileNavigation())
const styling = computed(() => getStyling())
const icons = computed(() => getIcons())
const seoData = computed(() => getSEOData())

// Close mobile menu when route changes
const route = useRoute()
watch(() => route.path, () => {
  closeMobileMenu()
})

// Close mobile menu when clicking outside
onMounted(() => {
  const handleClickOutside = (event) => {
    if (isMobileMenuOpen.value && !event.target.closest('header')) {
      closeMobileMenu()
    }
  }
  
  document.addEventListener('click', handleClickOutside)
  
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>
