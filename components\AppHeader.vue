<template>
  <header class="fixed top-0 w-full z-40 bg-white backdrop-blur-sm shadow-sm border-b border-gray-200">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between h-20">
        <NuxtLink :to="logo.link" class="flex items-center">
          <img :src="logo.src" :alt="logo.alt" :class="logo.classes" />
        </NuxtLink>

        <!-- Desktop Menu -->
        <nav class="hidden md:flex space-x-8">
          <NuxtLink 
            v-for="item in desktopNavigation" 
            :key="item.id" 
            :to="item.link"
            class="font-medium transition-colors duration-200 py-2 text-gray-700 hover:text-blue-600" 
          >
            {{ item.text }}
          </NuxtLink>
        </nav>

        <!-- Mobile Menu Button -->
        <button @click="toggleMobileMenu" class="md:hidden text-gray-800 p-2 rounded-md hover:bg-gray-100">
          <svg v-if="!mobileMenuOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
          <svg v-else class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div v-if="mobileMenuOpen" class="md:hidden bg-white shadow-md border-t border-gray-200">
      <div class="container mx-auto px-4 py-4">
        <div class="space-y-2">
          <NuxtLink 
            v-for="item in mobileNavigation" 
            :key="item.id" 
            :to="item.link" 
            class="block font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 py-3 px-3 rounded-md transition-colors duration-200"
            @click="mobileMenuOpen = false"
          >
            {{ item.text }}
          </NuxtLink>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
// Use header composable for data management
const {
  headerData,
  fetchHeaderData,
  getLogoData,
  getDesktopNavigation,
  getMobileNavigation
} = useHeader()

// Mobile menu state
const mobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

// Load header data on component mount
onMounted(async () => {
  await fetchHeaderData()
})

// Computed properties for template data
const logo = computed(() => getLogoData())
const desktopNavigation = computed(() => getDesktopNavigation())
const mobileNavigation = computed(() => getMobileNavigation())

// Close mobile menu when route changes
const route = useRoute()
watch(() => route.path, () => {
  mobileMenuOpen.value = false
})
</script>
