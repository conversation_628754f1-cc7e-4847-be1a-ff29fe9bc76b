<template>
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-4">
        <NuxtLink to="/">
          <img src="/logo-gemilang-loker.webp" alt="Logo" class="h-16 w-auto">
        </NuxtLink>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex space-x-8">
          <NuxtLink to="/" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">
            Beranda
          </NuxtLink>
          <NuxtLink to="/lowongan" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">
            Lowongan Kerja
          </NuxtLink>
          <NuxtLink to="/blog" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">
            Blog
          </NuxtLink>
          <NuxtLink to="/tentang" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">
            Tentang
          </NuxtLink>
          <NuxtLink to="/kontak" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">
            Kontak
          </NuxtLink>
        </nav>

        <!-- Mobile Menu Button -->
        <button
          @click="toggleMobileMenu"
          class="md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors"
          aria-label="Toggle mobile menu"
        >
          <svg v-if="!isMobileMenuOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
          <svg v-else class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <div
        v-show="isMobileMenuOpen"
        class="md:hidden border-t border-gray-200 py-4 space-y-2"
      >
        <NuxtLink
          to="/"
          @click="closeMobileMenu"
          class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"
        >
          Beranda
        </NuxtLink>
        <NuxtLink
          to="/lowongan"
          @click="closeMobileMenu"
          class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"
        >
          Lowongan Kerja
        </NuxtLink>
        <NuxtLink
          to="/blog"
          @click="closeMobileMenu"
          class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"
        >
          Blog
        </NuxtLink>
        <NuxtLink
          to="/tentang"
          @click="closeMobileMenu"
          class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"
        >
          Tentang
        </NuxtLink>
        <NuxtLink
          to="/kontak"
          @click="closeMobileMenu"
          class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors"
        >
          Kontak
        </NuxtLink>
      </div>
    </div>
  </header>
</template>

<script setup>
// Mobile menu state
const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

// Close mobile menu when route changes
const route = useRoute()
watch(() => route.path, () => {
  closeMobileMenu()
})

// Close mobile menu when clicking outside
onMounted(() => {
  const handleClickOutside = (event) => {
    if (isMobileMenuOpen.value && !event.target.closest('header')) {
      closeMobileMenu()
    }
  }

  document.addEventListener('click', handleClickOutside)

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>
