// Homepage Data Types

export interface HomepageButton {
  text: string
  link: string
  type: 'primary' | 'secondary' | 'external'
  style: string
  icon?: string
  target?: string
  rel?: string
}

export interface FloatingElement {
  position: string
  size: string
  color: string
  animation: string
}

export interface HomepageHero {
  title: string
  subtitle: string
  buttons: HomepageButton[]
  floatingElements: FloatingElement[]
}

export interface StatItem {
  id: number
  value: string
  label: string
  color: string
  source?: string
}

export interface HomepageStats {
  enabled: boolean
  title: string
  items: StatItem[]
}

export interface JobCategoriesSection {
  title: string
  subtitle: string
  note: string
  dynamicSource: string
  maxCategories: number
  linkPattern: string
}

export interface FeatureItem {
  id: number
  title: string
  description: string
  icon: string
  color: string
}

export interface HomepageFeatures {
  enabled: boolean
  title: string
  subtitle: string
  items: FeatureItem[]
}

export interface HowItWorksStep {
  id: number
  number: string
  title: string
  description: string
  color: string
}

export interface HowItWorksSection {
  title: string
  subtitle: string
  steps: HowItWorksStep[]
}

export interface TestimonialItem {
  id: number
  name: string
  position: string
  avatar: string
  avatarColor: string
  rating: number
  testimonial: string
}

export interface TestimonialsSection {
  title: string
  subtitle: string
  items: TestimonialItem[]
}

export interface BlogSection {
  title: string
  subtitle: string
  note: string
  dynamicSource: string
  viewAllLink: string
  viewAllText: string
}

export interface CTASection {
  title: string
  subtitle: string
  buttons: HomepageButton[]
}

export interface HomepageSEO {
  title: string
  metaDescription: string
  keywords: string
  ogTitle: string
  ogDescription: string
  ogType: string
  pageMetaTitle: string
}

export interface HomepageData {
  hero: HomepageHero
  stats: HomepageStats
  jobCategories: JobCategoriesSection
  features: HomepageFeatures
  howItWorks: HowItWorksSection
  testimonials: TestimonialsSection
  blog: BlogSection
  cta: CTASection
  seo: HomepageSEO
}

// Strapi Response Types (for future use)
export interface StrapiHomepageResponse {
  data: {
    id: number
    attributes: HomepageData & {
      createdAt: string
      updatedAt: string
      publishedAt: string
    }
  }
  meta: Record<string, any>
}

export interface StrapiHomepageListResponse {
  data: StrapiHomepageResponse['data'][]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}
