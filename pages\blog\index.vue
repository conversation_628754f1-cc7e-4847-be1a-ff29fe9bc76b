<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-700 text-white py-16">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-4">
            Blog Karir & Tips Kerja
          </h1>
          <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Temukan tips karir, panduan interview, tren industri, dan insight untuk mengembangkan karir impian Anda
          </p>
          
          <!-- Search Bar -->
          <div class="max-w-md mx-auto relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Cari artikel..."
              class="w-full pl-12 pr-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-300 focus:outline-none"
              @keyup.enter="handleSearch"
            >
            <svg class="absolute left-4 top-3.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-12">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Content -->
        <main class="lg:col-span-3">
          <!-- Filter Tabs -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-wrap gap-2 mb-4">
              <button
                @click="setActiveFilter('all')"
                :class="[
                  'px-4 py-2 rounded-lg font-semibold transition-colors',
                  activeFilter === 'all' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                Semua Artikel
              </button>
              <button
                @click="setActiveFilter('featured')"
                :class="[
                  'px-4 py-2 rounded-lg font-semibold transition-colors',
                  activeFilter === 'featured' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                Featured
              </button>
              <button
                v-for="category in categories"
                :key="category.slug"
                @click="setActiveFilter(category.slug)"
                :class="[
                  'px-4 py-2 rounded-lg font-semibold transition-colors',
                  activeFilter === category.slug 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                {{ category.name }}
              </button>
            </div>
            
            <!-- Sort Options -->
            <div class="flex items-center justify-between">
              <p class="text-gray-600">
                Menampilkan {{ posts.length }} dari {{ totalPosts }} artikel
              </p>
              <select
                v-model="sortBy"
                @change="handleSortChange"
                class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="newest">Terbaru</option>
                <option value="oldest">Terlama</option>
                <option value="popular">Terpopuler</option>
                <option value="title">Judul A-Z</option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="loading" class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p class="mt-2 text-gray-600">Memuat artikel...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <p class="text-red-600">{{ error }}</p>
            <button 
              @click="fetchPosts"
              class="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Coba Lagi
            </button>
          </div>

          <!-- Blog Posts Grid -->
          <div v-else-if="posts.length > 0" class="space-y-8">
            <!-- Featured Post (if exists) -->
            <div v-if="featuredPost && activeFilter === 'all'" class="bg-white rounded-lg shadow-md overflow-hidden">
              <div class="md:flex">
                <div class="md:w-1/2">
                  <img 
                    :src="featuredPost.featuredImage" 
                    :alt="featuredPost.title"
                    class="w-full h-64 md:h-full object-cover"
                  >
                </div>
                <div class="md:w-1/2 p-8">
                  <div class="flex items-center mb-4">
                    <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">
                      Featured
                    </span>
                    <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      {{ featuredPost.category }}
                    </span>
                  </div>
                  <h2 class="text-2xl font-bold text-gray-900 mb-4">
                    <NuxtLink :to="`/blog/${featuredPost.slug}`" class="hover:text-blue-600 transition-colors">
                      {{ featuredPost.title }}
                    </NuxtLink>
                  </h2>
                  <p class="text-gray-600 mb-4">{{ featuredPost.excerpt }}</p>
                  <div class="flex items-center text-sm text-gray-500 mb-4">
                    <time :datetime="featuredPost.publishedAt">{{ formatDate(featuredPost.publishedAt) }}</time>
                    <span class="mx-2">•</span>
                    <span>{{ featuredPost.readTime }} min read</span>
                    <span class="mx-2">•</span>
                    <span>{{ featuredPost.author }}</span>
                  </div>
                  <NuxtLink 
                    :to="`/blog/${featuredPost.slug}`"
                    class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold transition-colors"
                  >
                    Baca Selengkapnya
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                  </NuxtLink>
                </div>
              </div>
            </div>

            <!-- Regular Posts Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <BlogCard 
                v-for="post in regularPosts" 
                :key="post.id" 
                :post="post"
                @tag-click="handleTagClick"
              />
            </div>

            <!-- Pagination -->
            <div v-if="totalPages > 1" class="flex justify-center mt-12">
              <nav class="flex items-center space-x-2">
                <button
                  @click="goToPage(currentPage - 1)"
                  :disabled="currentPage === 1"
                  class="px-3 py-2 rounded-lg border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                
                <button
                  v-for="page in visiblePages"
                  :key="page"
                  @click="goToPage(page)"
                  :class="[
                    'px-3 py-2 rounded-lg border',
                    page === currentPage
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  ]"
                >
                  {{ page }}
                </button>
                
                <button
                  @click="goToPage(currentPage + 1)"
                  :disabled="currentPage === totalPages"
                  class="px-3 py-2 rounded-lg border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada artikel</h3>
            <p class="mt-1 text-sm text-gray-500">Belum ada artikel yang sesuai dengan filter Anda.</p>
          </div>
        </main>

        <!-- Sidebar -->
        <aside class="lg:col-span-1">
          <BlogSidebar 
            :recent-posts="recentPosts"
            :categories="categories"
            :tags="popularTags"
            @search="handleSidebarSearch"
            @category-click="handleCategoryClick"
            @tag-click="handleTagClick"
          />
        </aside>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'

// Meta tags
useHead({
  title: 'Blog Karir & Tips Kerja - Loker Gemilang',
   link: [
    {
      rel: 'canonical',
      href: 'https://lokergemilang.com/blog'
    }
  ],
  meta: [
    { name: 'description', content: 'Temukan tips karir, panduan interview, tren industri, dan insight untuk mengembangkan karir impian Anda di blog Loker Gemilang.' },
    { name: 'keywords', content: 'blog karir, tips kerja, panduan interview, tren industri, pengembangan karir' }
  ]
})

// Composables
const { fetchPosts, getFeaturedPosts, getRecentPosts, getCategories, getTags } = useBlog()

// Reactive data
const posts = ref([])
const recentPosts = ref([])
const categories = ref([])
const popularTags = ref([])
const loading = ref(false)
const error = ref(null)

const searchQuery = ref('')
const activeFilter = ref('all')
const sortBy = ref('newest')
const currentPage = ref(1)
const pageSize = ref(6)
const totalPosts = ref(0)
const totalPages = ref(0)

// Computed properties
const featuredPost = computed(() => {
  return posts.value.find(post => post.featured) || null
})

const regularPosts = computed(() => {
  if (activeFilter.value === 'all' && featuredPost.value) {
    return posts.value.filter(post => post.id !== featuredPost.value.id)
  }
  return posts.value
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const loadPosts = async () => {
  loading.value = true
  error.value = null
  
  try {
    const filters = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sort: sortBy.value
    }
    
    if (activeFilter.value === 'featured') {
      filters.featured = true
    } else if (activeFilter.value !== 'all') {
      filters.category = categories.value.find(cat => cat.slug === activeFilter.value)?.name
    }
    
    if (searchQuery.value) {
      filters.search = searchQuery.value
    }
    
    const response = await fetchPosts(filters)
    posts.value = response.data
    totalPosts.value = response.meta.total
    totalPages.value = response.meta.pageCount
  } catch (err) {
    error.value = 'Gagal memuat artikel. Silakan coba lagi.'
    console.error('Error loading posts:', err)
  } finally {
    loading.value = false
  }
}

const loadSidebarData = async () => {
  try {
    const [recentResponse, categoriesResponse, tagsResponse] = await Promise.all([
      getRecentPosts(5),
      getCategories(),
      getTags()
    ])
    
    recentPosts.value = recentResponse.data
    categories.value = categoriesResponse.data
    popularTags.value = tagsResponse.data.slice(0, 15).map(tag => tag.name)
  } catch (err) {
    console.error('Error loading sidebar data:', err)
  }
}

const setActiveFilter = (filter) => {
  activeFilter.value = filter
  currentPage.value = 1
  loadPosts()
}

const handleSearch = () => {
  currentPage.value = 1
  loadPosts()
}

const handleSidebarSearch = (query) => {
  searchQuery.value = query
  handleSearch()
}

const handleSortChange = () => {
  currentPage.value = 1
  loadPosts()
}

const handleCategoryClick = (categorySlug) => {
  setActiveFilter(categorySlug)
}

const handleTagClick = (tag) => {
  searchQuery.value = tag
  activeFilter.value = 'all'
  handleSearch()
}

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadPosts()
    
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadSidebarData(),
    loadPosts()
  ])
})

// Watch for route changes
watch(() => activeFilter.value, () => {
  loadPosts()
})
</script>
