# WhatsApp Configuration - Strapi Integration Guide

## 🎯 Overview

Dokumen ini menjelaskan strategi migrasi konfigurasi WhatsApp dari file statis ke Strapi CMS, memungkinkan admin untuk mengelola nomor WhatsApp berbeda untuk setiap lokasi melalui dashboard.

## 🏗️ Content Types Design

### 1. **WhatsApp Admin** (whatsapp-admin)

**Collection Type untuk mengelola data admin WhatsApp**

```javascript
{
  "kind": "collectionType",
  "collectionName": "whatsapp_admins",
  "info": {
    "singularName": "whatsapp-admin",
    "pluralName": "whatsapp-admins",
    "displayName": "WhatsApp Admin",
    "description": "Mengelola admin WhatsApp untuk berbagai lokasi"
  },
  "options": {
    "draftAndPublish": true
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true,
      "maxLength": 100,
      "minLength": 3
    },
    "phoneNumber": {
      "type": "string",
      "required": true,
      "regex": "^62[0-9]{9,13}$"
    },
    "isActive": {
      "type": "boolean",
      "default": true
    },
    "isDefault": {
      "type": "boolean",
      "default": false
    },
    "email": {
      "type": "email"
    },
    "description": {
      "type": "text",
      "maxLength": 500
    },
    "locations": {
      "type": "relation",
      "relation": "oneToMany",
      "target": "api::whatsapp-location.whatsapp-location",
      "mappedBy": "whatsappAdmin"
    }
  }
}
```

### 2. **WhatsApp Location** (whatsapp-location)

**Collection Type untuk mapping lokasi dengan admin WhatsApp**

```javascript
{
  "kind": "collectionType",
  "collectionName": "whatsapp_locations",
  "info": {
    "singularName": "whatsapp-location",
    "pluralName": "whatsapp-locations",
    "displayName": "WhatsApp Location",
    "description": "Mapping lokasi dengan admin WhatsApp"
  },
  "options": {
    "draftAndPublish": true
  },
  "attributes": {
    "provinsi": {
      "type": "string",
      "required": true
    },
    "kabupaten": {
      "type": "string"
    },
    "priority": {
      "type": "integer",
      "default": 1,
      "min": 1,
      "max": 10
    },
    "isActive": {
      "type": "boolean",
      "default": true
    },
    "whatsappAdmin": {
      "type": "relation",
      "relation": "manyToOne",
      "target": "api::whatsapp-admin.whatsapp-admin",
      "inversedBy": "locations"
    },
    "notes": {
      "type": "text"
    }
  }
}
```

### 3. **WhatsApp Settings** (whatsapp-setting)

**Single Type untuk pengaturan global WhatsApp**

```javascript
{
  "kind": "singleType",
  "collectionName": "whatsapp_setting",
  "info": {
    "singularName": "whatsapp-setting",
    "pluralName": "whatsapp-settings",
    "displayName": "WhatsApp Settings",
    "description": "Pengaturan global untuk WhatsApp"
  },
  "options": {
    "draftAndPublish": false
  },
  "attributes": {
    "defaultAdmin": {
      "type": "relation",
      "relation": "oneToOne",
      "target": "api::whatsapp-admin.whatsapp-admin"
    },
    "messageTemplate": {
      "type": "component",
      "repeatable": false,
      "component": "whatsapp.message-template"
    },
    "enableLocationRouting": {
      "type": "boolean",
      "default": true
    },
    "fallbackBehavior": {
      "type": "enumeration",
      "enum": ["use_default", "use_closest", "use_random"],
      "default": "use_default"
    }
  }
}
```

## 🧩 Components Design

### **Message Template Component**

```javascript
{
  "collectionName": "components_whatsapp_message_templates",
  "info": {
    "displayName": "Message Template",
    "description": "Template pesan WhatsApp untuk apply job"
  },
  "attributes": {
    "applyJobTemplate": {
      "type": "text",
      "required": true,
      "default": "Halo {adminName},\n\nSaya tertarik untuk melamar posisi *{jobTitle}* di {location}.\n\nDetail lowongan:\n- Posisi: {jobTitle}\n- Lokasi: {location}\n- Divisi: {division}\n- Pendidikan: {education}\n\nMohon informasi lebih lanjut mengenai proses recruitment.\n\nTerima kasih."
    },
    "shareJobTemplate": {
      "type": "text",
      "required": true,
      "default": "Lowongan kerja: {jobTitle} di {location} - {url}"
    },
    "variables": {
      "type": "json",
      "default": {
        "available": [
          "{adminName}",
          "{jobTitle}",
          "{location}",
          "{division}",
          "{education}",
          "{url}"
        ]
      }
    }
  }
}
```

## 🎨 Admin Interface Design

### **1. Dashboard Overview**

```
📊 WhatsApp Management Dashboard
┌─────────────────────────────────────────┐
│ 📱 Active Admins: 5                     │
│ 📍 Covered Locations: 12                │
│ 🔄 Default Admin: Admin Pusat           │
│ ⚠️  Unmapped Locations: 2               │
└─────────────────────────────────────────┘

Recent Activities:
- Admin Jakarta updated phone number
- New location mapping: Surabaya → Admin Jatim
- Template message updated
```

### **2. WhatsApp Admins Management**

```
┌─────────────────────────────────────────┐
│ ➕ Add New Admin    🔍 Search  📊 Export │
├─────────────────────────────────────────┤
│ Name              Phone         Status   │
│ Admin Jakarta     6281234567890 ✅ Active│
│ Admin Bandung     6281234567891 ✅ Active│
│ Admin Surabaya    6281234567892 ❌ Inactive│
│ Admin Default     6287777733566 🌟 Default│
└─────────────────────────────────────────┘

Form Fields:
- Name* (text)
- Phone Number* (format: 62XXXXXXXXXX)
- Email (optional)
- Is Active (toggle)
- Is Default (radio - only one allowed)
- Description (textarea)
```

### **3. Location Mapping Interface**

```
┌─────────────────────────────────────────┐
│ 🗺️ Location → Admin Mapping             │
├─────────────────────────────────────────┤
│ Provinsi: [Dropdown] ▼                  │
│ Kabupaten: [Dropdown] ▼ (optional)      │
│ Assigned Admin: [Dropdown] ▼            │
│ Priority: [1-10] ○○○○○                   │
│ [Save Mapping] [Cancel]                 │
├─────────────────────────────────────────┤
│ Current Mappings:                       │
│ 📍 DKI Jakarta → Admin Jakarta          │
│ 📍 Jawa Barat, Bandung → Admin Bandung  │
│ 📍 Jawa Timur → Admin Surabaya          │
└─────────────────────────────────────────┘
```

### **4. Message Templates**

```
┌─────────────────────────────────────────┐
│ 💬 Message Templates                     │
├─────────────────────────────────────────┤
│ Apply Job Template:                     │
│ ┌─────────────────────────────────────┐ │
│ │ Halo {adminName},                   │ │
│ │                                     │ │
│ │ Saya tertarik untuk melamar posisi  │ │
│ │ *{jobTitle}* di {location}.         │ │
│ │                                     │ │
│ │ Detail lowongan:                    │ │
│ │ - Posisi: {jobTitle}                │ │
│ │ - Lokasi: {location}                │ │
│ │ - Divisi: {division}                │ │
│ │ - Pendidikan: {education}           │ │
│ │                                     │ │
│ │ Mohon informasi lebih lanjut.       │ │
│ │                                     │ │
│ │ Terima kasih.                       │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ Available Variables:                    │
│ {adminName} {jobTitle} {location}       │
│ {division} {education} {url}            │
│                                         │
│ [Preview] [Save Changes]                │
└─────────────────────────────────────────┘
```

## 🔄 Migration Strategy

### **Phase 1: Content Types Setup**

1. **Create Content Types in Strapi:**
   ```bash
   # Install dan setup Strapi (jika belum)
   npx create-strapi-app@latest backend --quickstart
   
   # Buat content types sesuai schema di atas
   # Melalui admin panel atau generate via CLI
   ```

2. **Import Existing Data:**
   ```javascript
   // scripts/migrate-whatsapp-to-strapi.js
   import { whatsappConfig } from '../config/whatsapp.js'
   
   const migrateWhatsAppData = async () => {
     // 1. Create default admin
     const defaultAdmin = await strapi.entityService.create('api::whatsapp-admin.whatsapp-admin', {
       data: {
         name: whatsappConfig.default.name,
         phoneNumber: whatsappConfig.default.number,
         isDefault: true,
         isActive: true
       }
     })
   
     // 2. Create location-specific admins
     const admins = new Map()
     
     for (const [locationKey, config] of Object.entries(whatsappConfig.locations)) {
       const [provinsi, kabupaten] = locationKey.split('|')
       
       // Check if admin already exists
       let admin = admins.get(config.number)
       if (!admin) {
         admin = await strapi.entityService.create('api::whatsapp-admin.whatsapp-admin', {
           data: {
             name: config.name,
             phoneNumber: config.number,
             isActive: true
           }
         })
         admins.set(config.number, admin)
       }
       
       // Create location mapping
       await strapi.entityService.create('api::whatsapp-location.whatsapp-location', {
         data: {
           provinsi,
           kabupaten,
           whatsappAdmin: admin.id,
           isActive: true
         }
       })
     }
   
     // 3. Handle province-only mappings
     for (const [provinsi, config] of Object.entries(whatsappConfig.provinces)) {
       let admin = admins.get(config.number)
       if (!admin) {
         admin = await strapi.entityService.create('api::whatsapp-admin.whatsapp-admin', {
           data: {
             name: config.name,
             phoneNumber: config.number,
             isActive: true
           }
         })
       }
       
       await strapi.entityService.create('api::whatsapp-location.whatsapp-location', {
         data: {
           provinsi,
           whatsappAdmin: admin.id,
           isActive: true,
           priority: 2 // Lower priority than specific kabupaten
         }
       })
     }
   }
   ```

### **Phase 2: Update Composable**

```javascript
// composables/useWhatsAppStrapi.js
export const useWhatsAppStrapi = () => {
  const whatsappData = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Fetch WhatsApp configuration from Strapi
  const fetchWhatsAppConfig = async () => {
    loading.value = true
    error.value = null
    
    try {
      const { data } = await $fetch('/api/strapi/whatsapp-config')
      whatsappData.value = data
    } catch (err) {
      error.value = err
      console.error('Error loading WhatsApp config:', err)
    } finally {
      loading.value = false
    }
  }

  // Get WhatsApp admin for specific location
  const getLocationWhatsApp = (provinsi, kabupaten) => {
    if (!whatsappData.value) return null

    const { admins, locations, settings } = whatsappData.value

    // 1. Try exact match (provinsi + kabupaten)
    const exactMatch = locations.find(loc => 
      loc.provinsi === provinsi && 
      loc.kabupaten === kabupaten &&
      loc.isActive
    )
    
    if (exactMatch?.whatsappAdmin) {
      return exactMatch.whatsappAdmin
    }

    // 2. Try province-only match
    const provinceMatch = locations.find(loc => 
      loc.provinsi === provinsi && 
      !loc.kabupaten &&
      loc.isActive
    )
    
    if (provinceMatch?.whatsappAdmin) {
      return provinceMatch.whatsappAdmin
    }

    // 3. Use default admin
    return settings.defaultAdmin || admins.find(admin => admin.isDefault)
  }

  // Create WhatsApp apply URL
  const createApplyWhatsAppUrl = (job) => {
    const admin = getLocationWhatsApp(job.provinsi, job.kabupaten)
    if (!admin) return null

    const template = whatsappData.value.settings.messageTemplate.applyJobTemplate
    
    const message = template
      .replace('{adminName}', admin.name)
      .replace('{jobTitle}', job.judul)
      .replace('{location}', `${job.kabupaten}, ${job.provinsi}`)
      .replace('{division}', job.divisi)
      .replace('{education}', job.pendidikan)

    return `https://wa.me/${admin.phoneNumber}?text=${encodeURIComponent(message)}`
  }

  return {
    whatsappData: readonly(whatsappData),
    loading: readonly(loading),
    error: readonly(error),
    fetchWhatsAppConfig,
    getLocationWhatsApp,
    createApplyWhatsAppUrl
  }
}
```

### **Phase 3: API Endpoints**

```javascript
// server/api/strapi/whatsapp-config.get.ts
export default defineEventHandler(async (event) => {
  try {
    // Fetch all WhatsApp admins
    const admins = await $fetch(`${process.env.STRAPI_URL}/api/whatsapp-admins`, {
      params: {
        populate: '*',
        filters: {
          isActive: true
        }
      }
    })

    // Fetch all location mappings
    const locations = await $fetch(`${process.env.STRAPI_URL}/api/whatsapp-locations`, {
      params: {
        populate: 'whatsappAdmin',
        filters: {
          isActive: true
        },
        sort: 'priority:asc'
      }
    })

    // Fetch settings
    const settings = await $fetch(`${process.env.STRAPI_URL}/api/whatsapp-setting`, {
      params: {
        populate: ['defaultAdmin', 'messageTemplate']
      }
    })

    return {
      data: {
        admins: admins.data,
        locations: locations.data,
        settings: settings.data
      }
    }
  } catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Error fetching WhatsApp configuration'
    })
  }
})
```

## 🎯 Benefits of Strapi Integration

### **1. Admin Benefits:**
- ✅ **Visual Interface** - No coding required
- ✅ **Real-time Updates** - Changes apply immediately
- ✅ **Role-based Access** - Different permission levels
- ✅ **Audit Trail** - Track who changed what
- ✅ **Bulk Operations** - Import/export data easily

### **2. Technical Benefits:**
- ✅ **API-driven** - RESTful and GraphQL support
- ✅ **Scalable** - Handle thousands of locations
- ✅ **Cacheable** - Better performance
- ✅ **Validated** - Built-in data validation
- ✅ **Extensible** - Easy to add new features

### **3. Business Benefits:**
- ✅ **Flexibility** - Easy location management
- ✅ **Scalability** - Add new regions quickly
- ✅ **Consistency** - Centralized message templates
- ✅ **Analytics** - Track admin performance
- ✅ **Backup** - Data is safely stored

## 🚀 Implementation Timeline

### **Week 1: Setup**
- [ ] Create Strapi content types
- [ ] Setup admin permissions
- [ ] Create migration script

### **Week 2: Data Migration**
- [ ] Run migration script
- [ ] Validate data integrity
- [ ] Test admin interface

### **Week 3: Frontend Integration**
- [ ] Update composables
- [ ] Create API endpoints
- [ ] Test functionality

### **Week 4: Testing & Deployment**
- [ ] End-to-end testing
- [ ] Performance optimization
- [ ] Production deployment

## 📋 Admin Training Guide

### **1. Managing WhatsApp Admins:**
1. Go to **Content Manager → WhatsApp Admins**
2. Click **"Create new entry"**
3. Fill required fields:
   - Name (e.g., "Admin Jakarta")
   - Phone Number (format: 62XXXXXXXXXX)
   - Set as Active
4. Click **"Save"**

### **2. Location Mapping:**
1. Go to **Content Manager → WhatsApp Locations**
2. Click **"Create new entry"**
3. Select:
   - Provinsi (required)
   - Kabupaten (optional for province-wide)
   - WhatsApp Admin
   - Priority (1 = highest)
4. Click **"Save"**

### **3. Message Templates:**
1. Go to **Content Manager → WhatsApp Setting**
2. Edit **"Message Template"**
3. Use variables: `{adminName}`, `{jobTitle}`, etc.
4. Click **"Save"**

Dengan implementasi ini, admin dapat dengan mudah mengelola nomor WhatsApp untuk berbagai lokasi tanpa perlu coding! 🎉 