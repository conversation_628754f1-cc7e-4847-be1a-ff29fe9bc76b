{"metadata": {"version": "1.0", "lastUpdated": "2024-01-15", "description": "Konfigurasi WhatsApp admin berdasarkan lokasi untuk Tata Karya Gemilang. PT", "notes": "File ini akan dimigrasikan ke Strapi untuk memudahkan pengelolaan admin dashboard"}, "defaultAdmin": {"id": "default", "name": "Admin Recruitment", "phoneNumber": "6287777733566", "email": "<EMAIL>", "workingHours": "08:00-17:00", "isActive": true, "description": "Admin utama untuk semua lokasi yang belum memiliki admin khusus"}, "admins": [{"id": "admin-jakarta", "name": "Admin Jakarta", "phoneNumber": "6287777733566", "email": "<EMAIL>", "workingHours": "08:00-17:00", "isActive": true, "coverageArea": ["DKI Jakarta"], "description": "<PERSON><PERSON> k<PERSON> untuk wilayah DKI Jakarta"}, {"id": "admin-bandung", "name": "<PERSON><PERSON>", "phoneNumber": "6287777733566", "email": "<EMAIL>", "workingHours": "08:00-17:00", "isActive": true, "coverageArea": ["<PERSON>awa Barat"], "description": "<PERSON><PERSON> k<PERSON> untuk wilayah Jawa Barat"}, {"id": "admin-klaten", "name": "<PERSON><PERSON>", "phoneNumber": "6287777733566", "email": "<EMAIL>", "workingHours": "08:00-17:00", "isActive": true, "coverageArea": ["Jawa Tengah"], "description": "<PERSON><PERSON> k<PERSON> untuk wilayah Jawa Tengah"}, {"id": "admin-yogyakarta", "name": "<PERSON><PERSON>", "phoneNumber": "6287777733566", "email": "<EMAIL>", "workingHours": "08:00-17:00", "isActive": true, "coverageArea": ["D<PERSON>I.<PERSON>"], "description": "<PERSON><PERSON> k<PERSON> untuk wilayah D.I.Yo<PERSON>"}, {"id": "admin-surabaya", "name": "<PERSON><PERSON>", "phoneNumber": "6287777733566", "email": "<EMAIL>", "workingHours": "08:00-17:00", "isActive": true, "coverageArea": ["<PERSON><PERSON>"], "description": "<PERSON><PERSON> k<PERSON> untuk wilayah J<PERSON> Tim<PERSON>"}], "locationMappings": [{"id": "mapping-1", "provinsi": "DKI Jakarta", "kabupaten": "Jakarta Selatan", "adminId": "admin-jakarta", "priority": 1, "isActive": true, "notes": "Area prioritas tinggi dengan volume lowongan besar"}, {"id": "mapping-2", "provinsi": "DKI Jakarta", "kabupaten": "Jakarta Pusat", "adminId": "admin-jakarta", "priority": 1, "isActive": true, "notes": "Area kantor pusat"}, {"id": "mapping-3", "provinsi": "DKI Jakarta", "kabupaten": "Jakarta Barat", "adminId": "admin-jakarta", "priority": 1, "isActive": true}, {"id": "mapping-4", "provinsi": "DKI Jakarta", "kabupaten": null, "adminId": "admin-jakarta", "priority": 5, "isActive": true, "notes": "Fallback untuk seluruh DKI Jakarta"}, {"id": "mapping-5", "provinsi": "<PERSON>awa Barat", "kabupaten": "Bandung", "adminId": "admin-bandung", "priority": 1, "isActive": true}, {"id": "mapping-6", "provinsi": "<PERSON>awa Barat", "kabupaten": "<PERSON><PERSON>", "adminId": "admin-bandung", "priority": 1, "isActive": true}, {"id": "mapping-7", "provinsi": "<PERSON>awa Barat", "kabupaten": null, "adminId": "admin-bandung", "priority": 5, "isActive": true, "notes": "Fallback untuk seluruh Jawa Barat"}, {"id": "mapping-8", "provinsi": "Jawa Tengah", "kabupaten": "Klaten", "adminId": "admin-klaten", "priority": 1, "isActive": true}, {"id": "mapping-9", "provinsi": "Jawa Tengah", "kabupaten": "Semarang", "adminId": "admin-klaten", "priority": 1, "isActive": true}, {"id": "mapping-10", "provinsi": "Jawa Tengah", "kabupaten": null, "adminId": "admin-klaten", "priority": 5, "isActive": true, "notes": "Fallback untuk seluruh Jawa Tengah"}, {"id": "mapping-11", "provinsi": "D<PERSON>I.<PERSON>", "kabupaten": "<PERSON><PERSON><PERSON>", "adminId": "admin-yogyakarta", "priority": 1, "isActive": true}, {"id": "mapping-12", "provinsi": "D<PERSON>I.<PERSON>", "kabupaten": "Yogyakarta", "adminId": "admin-yogyakarta", "priority": 1, "isActive": true}, {"id": "mapping-13", "provinsi": "D<PERSON>I.<PERSON>", "kabupaten": null, "adminId": "admin-yogyakarta", "priority": 5, "isActive": true, "notes": "Fallback untuk seluruh D.I.Yo<PERSON>"}, {"id": "mapping-14", "provinsi": "<PERSON><PERSON>", "kabupaten": "Surabaya", "adminId": "admin-surabaya", "priority": 1, "isActive": true}, {"id": "mapping-15", "provinsi": "<PERSON><PERSON>", "kabupaten": null, "adminId": "admin-surabaya", "priority": 5, "isActive": true, "notes": "Fallback untuk se<PERSON><PERSON><PERSON>"}], "messageTemplates": {"applyJob": {"template": "Halo {adminName},\n\n<PERSON><PERSON> <PERSON><PERSON> untuk melamar posisi *{jobTitle}* di {location}.\n\nDetail lowongan:\n- Posisi: {jobTitle}\n- Lokasi: {location}\n- Divisi: {division}\n- Pendidikan: {education}\n- <PERSON><PERSON>: {gender}\n\nMohon informasi lebih lanjut mengenai proses recruitment untuk posisi ini.\n\nTerima kasih.", "variables": ["{adminName}", "{jobTitle}", "{location}", "{division}", "{education}", "{gender}", "{deadline}"]}, "shareJob": {"template": "🔥 Lowongan kerja: {jobTitle} di {location}\n📍 {division} - {education}\n⏰ Deadline: {deadline}\n🔗 {url}\n\n#LowonganKerja #TataKaryaGemilang #Karir", "variables": ["{jobTitle}", "{location}", "{division}", "{education}", "{deadline}", "{url}"]}}, "settings": {"enableLocationRouting": true, "fallbackBehavior": "use_default", "cacheTimeout": 300000, "enableAnalytics": true, "maxRetries": 3, "defaultLanguage": "id"}, "strapiMigration": {"isReady": true, "contentTypes": [{"name": "whatsapp-admin", "fields": ["name", "phoneNumber", "email", "workingHours", "isActive", "description"], "relations": ["locations"]}, {"name": "whatsapp-location", "fields": ["pro<PERSON><PERSON>", "kabupaten", "priority", "isActive", "notes"], "relations": ["whatsappAdmin"]}, {"name": "whatsapp-setting", "fields": ["defaultAdmin", "messageTemplates", "settings"], "type": "singleType"}]}}