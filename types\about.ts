// About page TypeScript interfaces - Strapi compatible

export interface AboutHero {
  title: string
  companyName: string
  subtitle: string
  backgroundImage?: string
}

export interface AboutImage {
  url: string
  alt: string
  caption?: string
  location?: string
}

export interface AboutVision {
  title: string
  content: string
}

export interface AboutMission {
  title: string
  items: string[]
}

export interface AboutCompanyOverview {
  title: string
  subtitle: string
  image: AboutImage
  vision: AboutVision
  mission: AboutMission
}

export interface AboutService {
  id: number
  title: string
  description: string
  color: string
  icon: string
  features: string[]
}

export interface AboutServices {
  title: string
  subtitle: string
  description: string
  serviceList: AboutService[]
}

export interface AboutAchievement {
  id: number
  number: string
  label: string
  color: string
}

export interface AboutStats {
  title: string
  subtitle: string
  description: string
  achievements: AboutAchievement[]
}

export interface AboutValue {
  id: number
  title: string
  description: string
  icon: string
  color: string
}

export interface AboutValues {
  title: string
  subtitle: string
  description: string
  valueList: AboutValue[]
}

export interface AboutTeamMember {
  id: number
  name: string
  position: string
  description: string
  image: string
  color: string
}

export interface AboutTeam {
  title: string
  subtitle: string
  description: string
  enabled: boolean
  members: AboutTeamMember[]
}

export interface AboutCTAButton {
  text: string
  link: string
  type: 'primary' | 'secondary'
  style: string
}

export interface AboutCTA {
  title: string
  description: string
  buttons: AboutCTAButton[]
}

export interface AboutSEO {
  title: string
  metaDescription: string
  keywords: string
  ogTitle: string
  ogDescription: string
  ogType: string
}

export interface AboutData {
  hero: AboutHero
  companyOverview: AboutCompanyOverview
  services: AboutServices
  stats: AboutStats
  values: AboutValues
  team: AboutTeam
  cta: AboutCTA
  seo: AboutSEO
}

// Strapi response interfaces
export interface StrapiAboutResponse {
  data: {
    id: number
    attributes: AboutData
  }
  meta: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export interface StrapiAboutData {
  id: number
  attributes: AboutData
}

// API Response interface
export interface AboutResponse {
  data: AboutData
  success: boolean
  message?: string
}
