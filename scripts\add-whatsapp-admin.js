// Script helper untuk menambahkan admin WhatsApp baru
// Usage: node scripts/add-whatsapp-admin.js

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Fungsi untuk menambahkan admin baru
function addWhatsAppAdmin(type, location, number, name) {
  const configPath = path.join(__dirname, '../config/whatsapp.js')
  
  try {
    // Baca file konfigurasi
    let configContent = fs.readFileSync(configPath, 'utf8')
    
    // Buat entry baru
    const newEntry = `    '${location}': {
      number: '${number}',
      name: '${name}'
    },`
    
    if (type === 'location') {
      // Tambahkan ke locations
      const locationPattern = /(locations:\s*{[^}]*)/
      if (locationPattern.test(configContent)) {
        configContent = configContent.replace(
          locationPattern,
          `$1\n${newEntry}`
        )
      }
    } else if (type === 'province') {
      // Tambahkan ke provinces
      const provincePattern = /(provinces:\s*{[^}]*)/
      if (provincePattern.test(configContent)) {
        configContent = configContent.replace(
          provincePattern,
          `$1\n${newEntry}`
        )
      }
    }
    
    // Tulis kembali file
    fs.writeFileSync(configPath, configContent)
    console.log(`✅ Admin WhatsApp berhasil ditambahkan:`)
    console.log(`   Type: ${type}`)
    console.log(`   Location: ${location}`)
    console.log(`   Number: ${number}`)
    console.log(`   Name: ${name}`)
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

// Contoh penggunaan
console.log('🔧 WhatsApp Admin Management Script')
console.log('=====================================')

// Uncomment dan edit sesuai kebutuhan:

// Menambah admin untuk lokasi spesifik (provinsi|kabupaten)
// addWhatsAppAdmin('location', 'Bali|Denpasar', '6281234567777', 'Admin Denpasar')

// Menambah admin untuk provinsi
// addWhatsAppAdmin('province', 'Bali', '6281234567666', 'Admin Bali')

console.log('💡 Edit script ini untuk menambahkan admin baru')
console.log('💡 Uncomment dan sesuaikan parameter addWhatsAppAdmin()')

// Export untuk digunakan di tempat lain
export { addWhatsAppAdmin }
