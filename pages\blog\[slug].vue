<template>
  <div :key="slug" class="min-h-screen bg-gray-50">
    <!-- Loading State -->
    <div v-if="pending" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Memuat artikel...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error || (!pending && !post)" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">Artikel Tidak Ditemukan</h1>
        <p class="text-gray-600 mb-6">Artikel yang Anda cari tidak dapat ditemukan.</p>
        <NuxtLink
          to="/blog"
          class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Ke<PERSON><PERSON> ke Blog
        </NuxtLink>
      </div>
    </div>

    <!-- Article Content -->
    <article v-else-if="post" class="pb-12">
      <!-- Hero Section -->
      <header class="relative">
        <!-- Featured Image -->
        <div class="relative h-96 md:h-[500px] overflow-hidden">
          <img 
            :src="post.featuredImage" 
            :alt="post.title"
            class="w-full h-full object-cover"
          >
          <div class="absolute inset-0 bg-black bg-opacity-40"></div>
        </div>

        <!-- Article Header -->
        <div class="absolute inset-0 flex items-end">
          <div class="container mx-auto px-4 pb-12">
            <div class="max-w-4xl">
              <!-- Breadcrumb -->
              <nav class="mb-4">
                <ol class="flex items-center space-x-2 text-sm text-white">
                  <li>
                    <NuxtLink to="/" class="hover:text-blue-300 transition-colors">Home</NuxtLink>
                  </li>
                  <li class="text-gray-300">/</li>
                  <li>
                    <NuxtLink to="/blog" class="hover:text-blue-300 transition-colors">Blog</NuxtLink>
                  </li>
                  <li class="text-gray-300">/</li>
                  <li class="text-gray-300">{{ post.title }}</li>
                </ol>
              </nav>

              <!-- Category & Featured Badge -->
              <div class="flex items-center mb-4">
                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">
                  {{ post.category }}
                </span>
                <span v-if="post.featured" class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Featured
                </span>
              </div>

              <!-- Title -->
              <h1 class="text-3xl md:text-5xl font-bold text-white mb-6 leading-tight">
                {{ post.title }}
              </h1>

              <!-- Meta Info -->
              <div class="flex flex-wrap items-center text-white text-sm space-x-6">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold mr-3">
                    {{ getAuthorInitials(post.author) }}
                  </div>
                  <div>
                    <p class="font-semibold">{{ post.author }}</p>
                    <p class="text-gray-300 text-xs">Author</p>
                  </div>
                </div>
                
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                  </svg>
                  <time :datetime="post.publishedAt">{{ formatDate(post.publishedAt) }}</time>
                </div>
                
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                  </svg>
                  <span>{{ post.readTime }} min read</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <div class="container mx-auto px-4 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- Article Content -->
          <main class="lg:col-span-3">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
              <!-- Article Excerpt -->
              <div class="text-xl text-gray-600 mb-8 pb-8 border-b border-gray-200 italic">
                {{ post.excerpt }}
              </div>

              <!-- Article Content -->
              <div class="prose prose-lg max-w-none">
                <div v-html="formatContent(post.content)"></div>
              </div>

              <!-- Tags -->
              <div class="mt-12 pt-8 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags:</h3>
                <div class="flex flex-wrap gap-2">
                  <NuxtLink
                    v-for="tag in post.tags"
                    :key="tag"
                    :to="`/blog?tag=${encodeURIComponent(tag)}`"
                    class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-blue-100 hover:text-blue-700 transition-colors"
                  >
                    #{{ tag }}
                  </NuxtLink>
                </div>
              </div>

              <!-- Share Buttons -->
              <div class="mt-8 pt-8 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Bagikan Artikel:</h3>
                <div class="flex space-x-4">
                  <button
                    @click="shareToFacebook"
                    class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    Facebook
                  </button>
                  
                  <button
                    @click="shareToTwitter"
                    class="flex items-center px-4 py-2 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors"
                  >
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                    Twitter
                  </button>
                  
                  <button
                    @click="shareToLinkedIn"
                    class="flex items-center px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors"
                  >
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                    LinkedIn
                  </button>
                  
                  <button
                    @click="copyToClipboard"
                    class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                      <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                    </svg>
                    Copy Link
                  </button>
                </div>
              </div>
            </div>

            <!-- Author Bio -->
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
              <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-white text-xl font-bold flex-shrink-0">
                  {{ getAuthorInitials(post.author) }}
                </div>
                <div class="flex-1">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">{{ post.author }}</h3>
                  <p class="text-gray-600 mb-4">
                    Penulis berpengalaman di bidang karir dan pengembangan profesional. 
                    Berbagi insight dan tips untuk membantu Anda mencapai kesuksesan karir.
                  </p>
                  <div class="flex space-x-3">
                    <a href="#" class="text-blue-600 hover:text-blue-800 transition-colors">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                    <a href="#" class="text-blue-400 hover:text-blue-600 transition-colors">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Related Posts -->
            <div v-if="relatedPosts.length > 0" class="bg-white rounded-lg shadow-md p-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-6">Artikel Terkait</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <article 
                  v-for="relatedPost in relatedPosts" 
                  :key="relatedPost.id"
                  class="group cursor-pointer"
                  @click="navigateToPost(relatedPost.slug)"
                >
                  <img 
                    :src="relatedPost.featuredImage" 
                    :alt="relatedPost.title"
                    class="w-full h-40 object-cover rounded-lg mb-4"
                  >
                  <h4 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
                    {{ relatedPost.title }}
                  </h4>
                  <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ relatedPost.excerpt }}</p>
                  <div class="flex items-center text-xs text-gray-500">
                    <time :datetime="relatedPost.publishedAt">{{ formatDate(relatedPost.publishedAt) }}</time>
                    <span class="mx-2">•</span>
                    <span>{{ relatedPost.readTime }} min read</span>
                  </div>
                </article>
              </div>
            </div>
          </main>

          <!-- Sidebar -->
          <aside class="lg:col-span-1">
            <BlogSidebar 
              :recent-posts="recentPosts"
              :categories="categories"
              :tags="popularTags"
              @search="handleSidebarSearch"
              @category-click="handleCategoryClick"
              @tag-click="handleTagClick"
            />
          </aside>
        </div>
      </div>
    </article>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'

// Route params
const route = useRoute()
const slug = computed(() => route.params.slug)

// Reactive data
const post = ref(null)
const relatedPosts = ref([])
const recentPosts = ref([])
const categories = ref([])
const popularTags = ref([])

// Loading states
const pending = ref(true)
const error = ref(null)

// Composables
const { fetchPostBySlug, getRelatedPosts, getRecentPosts, getCategories, getTags } = useBlog()

// Load post data
const loadPost = async () => {
  console.log('Loading post with slug:', slug.value)
  pending.value = true
  error.value = null

  try {
    const response = await fetchPostBySlug(slug.value)
    console.log('Post loaded successfully:', response.data)
    post.value = response.data

    // Set dynamic meta tags after post is loaded
    useHead({
      title: `${post.value.title} - Loker Gemilang`,
       link: [
    {
      rel: 'canonical',
      href: `https://lokergemilang.com/blog/${encodeURIComponent(post.value.slug)}`
    }
  ],
      meta: [
        { name: 'description', content: post.value.seo?.metaDescription || post.value.excerpt },
        { name: 'keywords', content: post.value.seo?.keywords?.join(', ') || post.value.tags.join(', ') },
        { property: 'og:title', content: post.value.seo?.metaTitle || post.value.title },
        { property: 'og:description', content: post.value.seo?.metaDescription || post.value.excerpt },
        { property: 'og:image', content: post.value.featuredImage },
        { property: 'og:type', content: 'article' },
        { property: 'article:author', content: post.value.author },
        { property: 'article:published_time', content: post.value.publishedAt },
        { property: 'article:section', content: post.value.category },
        { property: 'article:tag', content: post.value.tags.join(', ') }
      ]
    })

    // Load related data after post is loaded
    await loadRelatedData()

  } catch (err) {
    console.error('Error loading blog post:', err)
    error.value = err
  } finally {
    pending.value = false
  }
}

// Load sidebar and related data
const loadRelatedData = async () => {
  try {
    if (post.value) {
      const [relatedResponse, recentResponse, categoriesResponse, tagsResponse] = await Promise.all([
        getRelatedPosts(post.value.id, 4),
        getRecentPosts(5),
        getCategories(),
        getTags()
      ])

      relatedPosts.value = relatedResponse.data
      recentPosts.value = recentResponse.data
      categories.value = categoriesResponse.data
      popularTags.value = tagsResponse.data.slice(0, 15).map(tag => tag.name)
    }
  } catch (err) {
    console.error('Error loading related data:', err)
  }
}

// Load data on mount
onMounted(async () => {
  await nextTick()
  loadPost()
})

// Watch for route changes (when navigating between blog posts)
watch(() => route.params.slug, async (newSlug, oldSlug) => {
  if (newSlug && newSlug !== oldSlug) {
    await nextTick()
    loadPost()
  }
}, { immediate: false })

// Helper functions
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getAuthorInitials = (authorName) => {
  return authorName
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const formatContent = (content) => {
  // Convert markdown-like content to HTML
  return content
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/^/, '<p>')
    .replace(/$/, '</p>')
    .replace(/## (.*?)<br>/g, '<h2>$1</h2>')
    .replace(/### (.*?)<br>/g, '<h3>$1</h3>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/- (.*?)<br>/g, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>')
}

// Social sharing functions
const shareToFacebook = () => {
  const url = encodeURIComponent(window.location.href)
  window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank')
}

const shareToTwitter = () => {
  const url = encodeURIComponent(window.location.href)
  const text = encodeURIComponent(post.value.title)
  window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank')
}

const shareToLinkedIn = () => {
  const url = encodeURIComponent(window.location.href)
  window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank')
}

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(window.location.href)
    alert('Link berhasil disalin!')
  } catch (err) {
    console.error('Failed to copy:', err)
    alert('Gagal menyalin link')
  }
}

// Navigation functions
const navigateToPost = (postSlug) => {
  navigateTo(`/blog/${postSlug}`)
}

const handleSidebarSearch = (query) => {
  navigateTo(`/blog?search=${encodeURIComponent(query)}`)
}

const handleCategoryClick = (categorySlug) => {
  navigateTo(`/blog?category=${categorySlug}`)
}

const handleTagClick = (tag) => {
  navigateTo(`/blog?tag=${encodeURIComponent(tag)}`)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prose {
  color: #374151;
  line-height: 1.75;
}

.prose h2 {
  font-size: 1.5em;
  font-weight: 700;
  margin-top: 2em;
  margin-bottom: 1em;
  color: #111827;
}

.prose h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  color: #111827;
}

.prose p {
  margin-bottom: 1.25em;
}

.prose ul {
  margin: 1.25em 0;
  padding-left: 1.625em;
}

.prose li {
  margin: 0.5em 0;
}

.prose strong {
  font-weight: 600;
  color: #111827;
}

.prose em {
  font-style: italic;
}
</style>
