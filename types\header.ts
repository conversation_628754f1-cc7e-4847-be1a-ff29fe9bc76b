// Header Types
export interface HeaderLogo {
  src: string
  alt: string
  link: string
  height: string
  width: string
}

export interface NavigationItem {
  id: string
  text: string
  link: string
  style: string
}

export interface Navigation {
  desktop: {
    items: NavigationItem[]
  }
  mobile: {
    items: NavigationItem[]
  }
}

export interface HeaderStyling {
  header: string
  container: string
  flexContainer: string
  desktopNav: string
  mobileButton: string
  mobileMenu: string
}

export interface HeaderIcon {
  viewBox: string
  path: string
  strokeWidth: string
}

export interface HeaderIcons {
  hamburger: HeaderIcon
  close: HeaderIcon
}

export interface HeaderSEO {
  mobileMenuAriaLabel: string
}

export interface HeaderData {
  logo: HeaderLogo
  navigation: Navigation
  styling: HeaderStyling
  icons: HeaderIcons
  seo: HeaderSEO
}

// Strapi Response Types (for future use)
export interface StrapiHeaderResponse {
  data: {
    id: number
    attributes: HeaderData
  }
  meta: object
}

export interface StrapiHeaderListResponse {
  data: StrapiHeaderResponse[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}
