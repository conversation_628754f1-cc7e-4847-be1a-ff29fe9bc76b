// Contact Page Types

export interface ContactMetadata {
  version: string
  lastUpdated: string
  description: string
  notes: string
}

export interface ContactHero {
  title: string
  highlight: string
  subtitle: string
  backgroundGradient: string
}

export interface ContactCompany {
  name: string
  tagline: string
}

export interface ContactAddress {
  street: string
  district: string
  subDistrict: string
  city: string
  postalCode: string
  coordinates: {
    latitude: number
    longitude: number
  }
}

export interface ContactPhone {
  type: string
  number: string
  display: string
  isPrimary: boolean
}

export interface ContactEmail {
  type: string
  email: string
  label: string
  isPrimary: boolean
}

export interface ContactBusinessHours {
  weekdays: string
  hours: string
  timezone: string
  note: string
}

export interface ContactInfo {
  company: ContactCompany
  address: ContactAddress
  contact: {
    phones: ContactPhone[]
    emails: ContactEmail[]
  }
  businessHours: ContactBusinessHours
}

export interface SocialMediaLink {
  platform: string
  url: string
  label: string
  color: string
  isActive: boolean
}

export interface FormFieldOption {
  value: string
  label: string
}

export interface FormField {
  label: string
  placeholder: string
  required: boolean
  type: 'text' | 'email' | 'tel' | 'select' | 'textarea'
  rows?: number
  options?: FormFieldOption[]
}

export interface FormPrivacy {
  text: string
  privacyPolicyUrl: string
  termsUrl: string
  required: boolean
}

export interface ContactFormConfig {
  title: string
  submitButtonText: string
  submitButtonTextLoading: string
  successMessage: string
  errorMessage: string
  fields: {
    firstName: FormField
    lastName: FormField
    email: FormField
    phone: FormField
    subject: FormField
    message: FormField
  }
  privacy: FormPrivacy
}

export interface MapButton {
  text: string
  url: string
  type: 'primary' | 'secondary'
  icon: string
}

export interface MapConfig {
  title: string
  subtitle: string
  embedUrl: string
  buttons: MapButton[]
}

export interface FaqItem {
  question: string
  answer: string
  category: string
  isActive: boolean
}

export interface FaqConfig {
  title: string
  subtitle: string
  items: FaqItem[]
}

export interface CtaButton {
  text: string
  url: string
  type: 'primary' | 'secondary'
  style: string
  external?: boolean
}

export interface CtaConfig {
  title: string
  subtitle: string
  buttons: CtaButton[]
}

export interface SeoConfig {
  title: string
  description: string
  keywords: string
  canonicalUrl: string
  ogTitle: string
  ogDescription: string
  ogUrl: string
  ogImage: string
}

export interface SchemaAddress {
  streetAddress: string
  addressLocality: string
  addressRegion: string
  postalCode: string
  addressCountry: string
}

export interface SchemaContactPoint {
  telephone: string
  contactType: string
  availableLanguage: string[]
}

export interface SchemaOrganization {
  name: string
  legalName: string
  url: string
  logo: string
  foundingDate: string
  address: SchemaAddress
  contactPoint: SchemaContactPoint
  sameAs: string[]
}

export interface SchemaConfig {
  organization: SchemaOrganization
}

export interface StrapiContentType {
  name: string
  fields: string[]
  type: 'collection' | 'singleType'
}

export interface StrapiMigrationInfo {
  isReady: boolean
  contentTypes: StrapiContentType[]
}

// Main Contact Configuration Interface
export interface ContactConfiguration {
  metadata: ContactMetadata
  hero: ContactHero
  contactInfo: ContactInfo
  socialMedia: SocialMediaLink[]
  contactForm: ContactFormConfig
  map: MapConfig
  faq: FaqConfig
  cta: CtaConfig
  seo: SeoConfig
  schema: SchemaConfig
  strapiMigration: StrapiMigrationInfo
}

// Utility Types
export interface FormattedAddress {
  full: string
  lines: string[]
  coordinates: {
    latitude: number
    longitude: number
  }
}

export interface PrimaryContact {
  phone: ContactPhone | null
  email: ContactEmail | null
  businessHours: ContactBusinessHours
}

export interface FaqCategory {
  value: string
  label: string
  count: number
}

export interface ContactFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  subject: string
  message: string
  agreePrivacy: boolean
}

export interface ContactFormSubmissionResponse {
  success: boolean
  message: string
  errors?: string[]
}

// Structured Data Types
export interface OrganizationSchema {
  '@context': string
  '@type': string
  name: string
  legalName: string
  url: string
  logo: string
  foundingDate: string
  address: {
    '@type': string
    streetAddress: string
    addressLocality: string
    addressRegion: string
    postalCode: string
    addressCountry: string
  }
  contactPoint: {
    '@type': string
    telephone: string
    contactType: string
    availableLanguage: string[]
  }
  sameAs: string[]
}

export interface ContactPageSchema {
  '@context': string
  '@type': string
  name: string
  description: string
  url: string
  mainEntity: {
    '@type': string
    name: string
    telephone: string
    email: string
    address: {
      '@type': string
      streetAddress: string
      addressLocality: string
      addressRegion: string
      postalCode: string
      addressCountry: string
    }
    geo: {
      '@type': string
      latitude: number
      longitude: number
    }
    openingHours: string
  }
}

// Composable Return Type
export interface UseKontakReturn {
  // State
  kontakData: Readonly<Ref<ContactConfiguration | null>>
  loading: Readonly<Ref<boolean>>
  error: Readonly<Ref<Error | null>>
  
  // Core methods
  fetchKontakData: () => Promise<void>
  
  // Data getters
  getFormattedAddress: () => FormattedAddress | null
  getPrimaryContact: () => PrimaryContact | null
  getActiveSocialMedia: () => SocialMediaLink[]
  getFaqByCategory: (category?: string) => FaqItem[]
  getFaqCategories: () => FaqCategory[]
  getFormConfig: () => ContactFormConfig | null
  getMapConfig: () => MapConfig | null
  getCtaConfig: () => CtaConfig | null
  getSeoConfig: () => SeoConfig | null
  
  // Utility methods
  generateWhatsAppUrl: (message?: string) => string | null
  generateEmailUrl: (subject?: string) => string | null
  submitContactForm: (formData: ContactFormData) => Promise<ContactFormSubmissionResponse>
  generateStructuredData: () => OrganizationSchema | null
  generateContactPageSchema: () => ContactPageSchema | null
  getCategoryLabel: (category: string) => string
  
  // Computed
  isReady: ComputedRef<boolean>
  companyInfo: ComputedRef<ContactCompany | null>
  metadata: ComputedRef<ContactMetadata | null>
} 