// TypeScript interfaces untuk Blog data
// Dirancang untuk kompatibilitas dengan Strapi CMS

export interface BlogPost {
  id: number
  title: string
  slug: string
  excerpt: string
  content: string
  featuredImage: string
  category: string
  tags: string[]
  author: string
  publishedAt: string
  readTime: number
  featured: boolean
  status: 'published' | 'draft' | 'archived'
  seo: {
    metaTitle?: string
    metaDescription?: string
    keywords?: string[]
  }
  // Fields yang akan ditambahkan di Strapi
  createdAt?: string
  updatedAt?: string
  viewCount?: number
  likeCount?: number
  shareCount?: number
  relatedPosts?: number[]
}

export interface BlogCategory {
  id: number
  name: string
  slug: string
  description: string
  color: string
  icon?: string
  postCount?: number
}

export interface BlogAuthor {
  id: number
  name: string
  slug: string
  bio: string
  avatar: string
  email?: string
  social?: {
    twitter?: string
    linkedin?: string
    instagram?: string
  }
  postCount?: number
}

export interface BlogFilters {
  category?: string
  tag?: string
  author?: string
  featured?: boolean
  status?: string
  search?: string
  page?: number
  pageSize?: number
  sort?: string
  dateFrom?: string
  dateTo?: string
}

export interface BlogResponse {
  data: BlogPost[]
  meta: {
    total: number
    page: number
    pageSize: number
    pageCount: number
  }
}

export interface SingleBlogResponse {
  data: BlogPost
}

export interface BlogStats {
  totalPosts: number
  totalCategories: number
  totalAuthors: <AUTHORS>
  totalViews: number
  byCategory: {
    [key: string]: number
  }
  byMonth: {
    [key: string]: number
  }
}

export interface BlogSidebar {
  recentPosts: BlogPost[]
  popularPosts: BlogPost[]
  categories: BlogCategory[]
  tags: string[]
  archives: {
    month: string
    year: number
    count: number
  }[]
}

// Untuk Strapi response format
export interface StrapiBlogPost {
  id: number
  attributes: {
    title: string
    slug: string
    excerpt: string
    content: string
    featuredImage: {
      data?: {
        id: number
        attributes: {
          url: string
          alternativeText?: string
        }
      }
    }
    category: {
      data?: {
        id: number
        attributes: BlogCategory
      }
    }
    author: {
      data?: {
        id: number
        attributes: BlogAuthor
      }
    }
    tags: {
      data: {
        id: number
        attributes: {
          name: string
          slug: string
        }
      }[]
    }
    publishedAt: string
    createdAt: string
    updatedAt: string
    readTime: number
    featured: boolean
    status: string
    seo: {
      metaTitle?: string
      metaDescription?: string
      keywords?: string[]
    }
    viewCount?: number
    likeCount?: number
    shareCount?: number
  }
}

export interface StrapiBlogResponse {
  data: StrapiBlogPost[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}
