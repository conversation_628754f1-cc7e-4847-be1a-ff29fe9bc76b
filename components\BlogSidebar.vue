<template>
  <aside class="space-y-8">
    <!-- Search -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h3 class="text-lg font-bold text-gray-900 mb-4"><PERSON><PERSON> Artikel</h3>
      <div class="relative">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Cari artikel..."
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          @keyup.enter="handleSearch"
        >
        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>

    <!-- Recent Posts -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Artikel Terbaru</h3>
      <div class="space-y-4">
        <article 
          v-for="post in recentPosts" 
          :key="post.id"
          class="flex space-x-3 group cursor-pointer"
          @click="navigateToPost(post.slug)"
        >
          <img 
            :src="post.featuredImage" 
            :alt="post.title"
            class="w-16 h-16 object-cover rounded-lg flex-shrink-0"
          >
          <div class="flex-1 min-w-0">
            <h4 class="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {{ post.title }}
            </h4>
            <div class="flex items-center text-xs text-gray-500 mt-1">
              <time :datetime="post.publishedAt">{{ formatDate(post.publishedAt) }}</time>
              <span class="mx-1">•</span>
              <span>{{ post.readTime }} min</span>
            </div>
          </div>
        </article>
      </div>
    </div>

    <!-- Categories -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Kategori</h3>
      <div class="space-y-2">
        <div 
          v-for="category in categories" 
          :key="category.slug"
          class="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          @click="handleCategoryClick(category.slug)"
        >
          <span class="text-gray-700 hover:text-blue-600 transition-colors">
            {{ category.name }}
          </span>
          <span class="bg-gray-200 text-gray-600 px-2 py-1 rounded-full text-xs">
            {{ category.postCount }}
          </span>
        </div>
      </div>
    </div>

    <!-- Popular Tags -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Tag Populer</h3>
      <div class="flex flex-wrap gap-2">
        <span 
          v-for="tag in tags" 
          :key="tag"
          class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-blue-100 hover:text-blue-700 cursor-pointer transition-colors"
          @click="handleTagClick(tag)"
        >
          #{{ tag }}
        </span>
      </div>
    </div>

    <!-- Newsletter Signup -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-md p-6 text-white">
      <h3 class="text-lg font-bold mb-2">Newsletter</h3>
      <p class="text-blue-100 text-sm mb-4">
        Dapatkan artikel terbaru dan tips karir langsung di email Anda.
      </p>
      <form @submit.prevent="handleNewsletterSignup" class="space-y-3">
        <input
          v-model="newsletterEmail"
          type="email"
          placeholder="Email Anda"
          class="w-full px-3 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-300 focus:outline-none"
          required
        >
        <button
          type="submit"
          class="w-full bg-white text-blue-600 py-2 px-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          :disabled="newsletterLoading"
        >
          {{ newsletterLoading ? 'Mendaftar...' : 'Berlangganan' }}
        </button>
      </form>
    </div>

    <!-- Social Media -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Ikuti Kami</h3>
      <div class="flex space-x-3">
        <a 
          href="#" 
          class="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          aria-label="Facebook"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
        </a>
        <a 
          href="#" 
          class="flex items-center justify-center w-10 h-10 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors"
          aria-label="Twitter"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
          </svg>
        </a>
        <a 
          href="#" 
          class="flex items-center justify-center w-10 h-10 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors"
          aria-label="LinkedIn"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          </svg>
        </a>
        <a 
          href="#" 
          class="flex items-center justify-center w-10 h-10 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
          aria-label="Instagram"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
          </svg>
        </a>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Props
const props = defineProps({
  recentPosts: {
    type: Array,
    default: () => []
  },
  categories: {
    type: Array,
    default: () => []
  },
  tags: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['search', 'category-click', 'tag-click'])

// Reactive data
const searchQuery = ref('')
const newsletterEmail = ref('')
const newsletterLoading = ref(false)

// Methods
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value.trim())
  }
}

const handleCategoryClick = (categorySlug) => {
  emit('category-click', categorySlug)
}

const handleTagClick = (tag) => {
  emit('tag-click', tag)
}

const navigateToPost = (slug) => {
  navigateTo(`/blog/${slug}`)
}

const handleNewsletterSignup = async () => {
  if (!newsletterEmail.value) return
  
  newsletterLoading.value = true
  
  try {
    // TODO: Implement newsletter signup API call
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
    
    // Show success message
    alert('Terima kasih! Anda telah berlangganan newsletter kami.')
    newsletterEmail.value = ''
  } catch (error) {
    console.error('Newsletter signup error:', error)
    alert('Terjadi kesalahan. Silakan coba lagi.')
  } finally {
    newsletterLoading.value = false
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
