// Plugin untuk Strapi client (akan digunakan setelah migrasi)

export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig()
  
  // Strapi client configuration
  const strapiClient = {
    baseURL: config.public.strapiUrl,
    
    // Generic fetch method
    async fetch(endpoint: string, options: any = {}) {
      const url = `${this.baseURL}${endpoint}`
      
      const defaultOptions = {
        headers: {
          'Content-Type': 'application/json',
          ...(options.headers || {})
        }
      }
      
      return await $fetch(url, {
        ...defaultOptions,
        ...options
      })
    },
    
    // Jobs specific methods
    jobs: {
      async findMany(filters: any = {}) {
        return await strapiClient.fetch('/jobs', {
          query: {
            populate: '*',
            ...filters
          }
        })
      },
      
      async findOne(id: string | number) {
        return await strapiClient.fetch(`/jobs/${id}`, {
          query: {
            populate: '*'
          }
        })
      },
      
      async findBySlug(slug: string) {
        return await strapiClient.fetch('/jobs', {
          query: {
            filters: {
              slug: {
                $eq: slug
              }
            },
            populate: '*'
          }
        })
      }
    }
  }
  
  return {
    provide: {
      strapi: strapiClient
    }
  }
})