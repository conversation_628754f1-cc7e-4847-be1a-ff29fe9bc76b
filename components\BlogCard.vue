<template>
  <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <!-- Featured Image -->
    <div class="relative">
      <img 
        :src="post.featuredImage" 
        :alt="post.title"
        class="w-full h-48 object-cover"
        loading="lazy"
      >
      <!-- Featured Badge -->
      <div v-if="post.featured" class="absolute top-3 left-3">
        <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
          Featured
        </span>
      </div>
      <!-- Category Badge -->
      <div class="absolute top-3 right-3">
        <span class="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
          {{ post.category }}
        </span>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Meta Info -->
      <div class="flex items-center text-sm text-gray-500 mb-3">
        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
        </svg>
        <time :datetime="post.publishedAt">{{ formatDate(post.publishedAt) }}</time>
        
        <span class="mx-2">•</span>
        
        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
        </svg>
        <span>{{ post.readTime }} min read</span>
      </div>

      <!-- Title -->
      <h3 class="text-xl font-bold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
        <NuxtLink :to="`/blog/${post.slug}`">
          {{ post.title }}
        </NuxtLink>
      </h3>

      <!-- Excerpt -->
      <p class="text-gray-600 mb-4 line-clamp-3">
        {{ post.excerpt }}
      </p>

      <!-- Tags -->
      <div class="flex flex-wrap gap-2 mb-4">
        <span 
          v-for="tag in post.tags.slice(0, 3)" 
          :key="tag"
          class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs hover:bg-gray-200 transition-colors cursor-pointer"
          @click="$emit('tag-click', tag)"
        >
          #{{ tag }}
        </span>
        <span v-if="post.tags.length > 3" class="text-gray-500 text-xs">
          +{{ post.tags.length - 3 }} more
        </span>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-between">
        <!-- Author -->
        <div class="flex items-center">
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
            {{ getAuthorInitials(post.author) }}
          </div>
          <span class="ml-2 text-sm text-gray-700">{{ post.author }}</span>
        </div>

        <!-- Read More -->
        <NuxtLink 
          :to="`/blog/${post.slug}`"
          class="text-blue-600 hover:text-blue-800 font-semibold text-sm flex items-center transition-colors"
        >
          Baca Selengkapnya
          <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </NuxtLink>
      </div>
    </div>
  </article>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  post: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['tag-click'])

// Helper functions
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getAuthorInitials = (authorName) => {
  return authorName
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
