export const useHeader = () => {
  // State management
  const headerData = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Fetch header data
  const fetchHeaderData = async () => {
    loading.value = true
    error.value = null
    
    try {
      // For now, load from JSON file
      // When migrating to Strapi, replace this with API call
      const { default: data } = await import('~/data/header.json')
      headerData.value = data
      
      // Future Strapi implementation:
      // const response = await $fetch('/api/header')
      // headerData.value = response.data.attributes
      
    } catch (err) {
      error.value = err
      console.error('Error loading header data:', err)
    } finally {
      loading.value = false
    }
  }

  // Get logo data
  const getLogoData = () => {
    return headerData.value?.logo || {}
  }

  // Get desktop navigation items
  const getDesktopNavigation = () => {
    return headerData.value?.navigation?.desktop?.items || []
  }

  // Get mobile navigation items
  const getMobileNavigation = () => {
    return headerData.value?.navigation?.mobile?.items || []
  }

  // Get styling classes
  const getStyling = () => {
    return headerData.value?.styling || {}
  }

  // Get icons
  const getIcons = () => {
    return headerData.value?.icons || {}
  }

  // Get SEO data
  const getSEOData = () => {
    return headerData.value?.seo || {}
  }

  // Helper function to check if navigation item is active
  const isActiveRoute = (link) => {
    const route = useRoute()
    if (link === '/') {
      return route.path === '/'
    }
    return route.path.startsWith(link)
  }

  // Helper function to get active navigation classes
  const getNavigationClasses = (item, isMobile = false) => {
    const baseClasses = item.style
    const isActive = isActiveRoute(item.link)
    
    if (isActive) {
      if (isMobile) {
        return `${baseClasses} bg-blue-50 text-blue-600`
      } else {
        return `${baseClasses} text-blue-600`
      }
    }
    
    return baseClasses
  }

  return {
    // State
    headerData: readonly(headerData),
    loading: readonly(loading),
    error: readonly(error),
    
    // Methods
    fetchHeaderData,
    getLogoData,
    getDesktopNavigation,
    getMobileNavigation,
    getStyling,
    getIcons,
    getSEOData,
    isActiveRoute,
    getNavigationClasses
  }
}
