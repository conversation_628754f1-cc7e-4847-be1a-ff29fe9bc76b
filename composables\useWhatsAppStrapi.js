// Composable untuk WhatsApp integration dengan Strapi
export const useWhatsAppStrapi = () => {
  // State management
  const whatsappData = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const lastFetch = ref(null)

  // Cache duration (5 minutes)
  const CACHE_DURATION = 5 * 60 * 1000

  // Fetch WhatsApp configuration from Strapi
  const fetchWhatsAppConfig = async (forceRefresh = false) => {
    // Check cache
    if (!forceRefresh && whatsappData.value && lastFetch.value) {
      const timeDiff = Date.now() - lastFetch.value
      if (timeDiff < CACHE_DURATION) {
        return whatsappData.value
      }
    }

    loading.value = true
    error.value = null
    
    try {
      // Fetch from Strapi API
      const { data } = await $fetch('/api/strapi/whatsapp-config')
      whatsappData.value = data
      lastFetch.value = Date.now()
      
      return data
    } catch (err) {
      error.value = err
      console.error('Error loading WhatsApp config from Strapi:', err)
      
      // Fallback ke config lama jika Strapi gagal
      const fallbackConfig = await import('~/config/whatsapp.js')
      return transformLegacyConfig(fallbackConfig.whatsappConfig)
      
    } finally {
      loading.value = false
    }
  }

  // Transform legacy config to new format (untuk backward compatibility)
  const transformLegacyConfig = (legacyConfig) => {
    const admins = []
    const locations = []
    const adminMap = new Map()

    // Process default admin
    const defaultAdmin = {
      id: 'default',
      name: legacyConfig.default.name,
      phoneNumber: legacyConfig.default.number,
      isDefault: true,
      isActive: true
    }
    admins.push(defaultAdmin)
    adminMap.set(legacyConfig.default.number, defaultAdmin)

    // Process location-specific admins
    Object.entries(legacyConfig.locations || {}).forEach(([locationKey, config]) => {
      const [provinsi, kabupaten] = locationKey.split('|')
      
      // Get or create admin
      let admin = adminMap.get(config.number)
      if (!admin) {
        admin = {
          id: `admin-${adminMap.size}`,
          name: config.name,
          phoneNumber: config.number,
          isDefault: false,
          isActive: true
        }
        admins.push(admin)
        adminMap.set(config.number, admin)
      }

      // Create location mapping
      locations.push({
        id: `loc-${locations.length}`,
        provinsi,
        kabupaten,
        priority: 1,
        isActive: true,
        whatsappAdmin: admin
      })
    })

    // Process province-only admins
    Object.entries(legacyConfig.provinces || {}).forEach(([provinsi, config]) => {
      let admin = adminMap.get(config.number)
      if (!admin) {
        admin = {
          id: `admin-${adminMap.size}`,
          name: config.name,
          phoneNumber: config.number,
          isDefault: false,
          isActive: true
        }
        admins.push(admin)
        adminMap.set(config.number, admin)
      }

      locations.push({
        id: `loc-${locations.length}`,
        provinsi,
        kabupaten: null,
        priority: 5,
        isActive: true,
        whatsappAdmin: admin
      })
    })

    return {
      admins,
      locations,
      settings: {
        defaultAdmin: defaultAdmin,
        applyJobTemplate: `Halo {adminName},

Saya tertarik untuk melamar posisi *{jobTitle}* di {location}.

Detail lowongan:
- Posisi: {jobTitle}
- Lokasi: {location}
- Divisi: {division}
- Pendidikan: {education}
- Jenis Kelamin: {gender}

Mohon informasi lebih lanjut mengenai proses recruitment untuk posisi ini.

Terima kasih.`,
        shareJobTemplate: 'Lowongan kerja: {jobTitle} di {location} - {url}'
      }
    }
  }

  // Get WhatsApp admin for specific location with priority logic
  const getLocationWhatsApp = (provinsi, kabupaten) => {
    if (!whatsappData.value) {
      console.warn('WhatsApp data not loaded')
      return null
    }

    const { locations, settings } = whatsappData.value

    // Filter active locations for the province
    const activeLocations = locations.filter(loc => 
      loc.provinsi === provinsi && loc.isActive
    )

    if (activeLocations.length === 0) {
      console.log(`No active locations found for ${provinsi}`)
      return settings.defaultAdmin
    }

    // 1. Try exact match (provinsi + kabupaten) with highest priority
    const exactMatches = activeLocations.filter(loc => 
      loc.kabupaten === kabupaten
    ).sort((a, b) => a.priority - b.priority)

    if (exactMatches.length > 0 && exactMatches[0].whatsappAdmin) {
      return exactMatches[0].whatsappAdmin
    }

    // 2. Try province-only match (kabupaten = null) with highest priority
    const provinceMatches = activeLocations.filter(loc => 
      !loc.kabupaten || loc.kabupaten === ''
    ).sort((a, b) => a.priority - b.priority)

    if (provinceMatches.length > 0 && provinceMatches[0].whatsappAdmin) {
      return provinceMatches[0].whatsappAdmin
    }

    // 3. Use default admin as fallback
    console.log(`Using default admin for ${provinsi}, ${kabupaten}`)
    return settings.defaultAdmin
  }

  // Create WhatsApp apply URL with template
  const createApplyWhatsAppUrl = (job) => {
    const admin = getLocationWhatsApp(job.provinsi, job.kabupaten)
    if (!admin || !admin.phoneNumber) {
      console.error('No admin found for location:', job.provinsi, job.kabupaten)
      return null
    }

    const template = whatsappData.value?.settings?.applyJobTemplate || `Halo {adminName},

Saya tertarik untuk melamar posisi *{jobTitle}* di {location}.

Detail lowongan:
- Posisi: {jobTitle}
- Lokasi: {location}
- Divisi: {division}
- Pendidikan: {education}

Mohon informasi lebih lanjut.

Terima kasih.`

    // Replace template variables
    const message = template
      .replace(/{adminName}/g, admin.name)
      .replace(/{jobTitle}/g, job.judul)
      .replace(/{location}/g, `${job.kabupaten}, ${job.provinsi}`)
      .replace(/{division}/g, job.divisi)
      .replace(/{education}/g, job.pendidikan)
      .replace(/{gender}/g, job.jenisKelamin)
      .replace(/{deadline}/g, job.deadline)

    return `https://wa.me/${admin.phoneNumber}?text=${encodeURIComponent(message)}`
  }

  // Create WhatsApp share URL
  const createShareWhatsAppUrl = (job, url = null) => {
    const shareUrl = url || window.location.href
    const template = whatsappData.value?.settings?.shareJobTemplate || 
      'Lowongan kerja: {jobTitle} di {location} - {url}'

    const message = template
      .replace(/{jobTitle}/g, job.judul)
      .replace(/{location}/g, `${job.kabupaten}, ${job.provinsi}`)
      .replace(/{division}/g, job.divisi)
      .replace(/{education}/g, job.pendidikan)
      .replace(/{url}/g, shareUrl)

    return `https://wa.me/?text=${encodeURIComponent(message)}`
  }

  // Apply via WhatsApp with error handling
  const applyViaWhatsApp = async (job) => {
    try {
      // Ensure config is loaded
      if (!whatsappData.value) {
        await fetchWhatsAppConfig()
      }

      const whatsappUrl = createApplyWhatsAppUrl(job)
      if (whatsappUrl) {
        window.open(whatsappUrl, '_blank')
      } else {
        throw new Error('Could not generate WhatsApp URL')
      }
    } catch (error) {
      console.error('Error applying via WhatsApp:', error)
      // Fallback to default
      const fallbackUrl = `https://wa.me/6287777733566?text=${encodeURIComponent(`Saya tertarik untuk melamar posisi ${job.judul} di ${job.kabupaten}, ${job.provinsi}`)}`
      window.open(fallbackUrl, '_blank')
    }
  }

  // Share via WhatsApp
  const shareViaWhatsApp = async (job, url = null) => {
    try {
      if (!whatsappData.value) {
        await fetchWhatsAppConfig()
      }

      const whatsappUrl = createShareWhatsAppUrl(job, url)
      window.open(whatsappUrl, '_blank')
    } catch (error) {
      console.error('Error sharing via WhatsApp:', error)
    }
  }

  // Get admin statistics
  const getAdminStats = () => {
    if (!whatsappData.value) return null

    const { admins, locations } = whatsappData.value
    
    return {
      totalAdmins: admins.filter(admin => admin.isActive).length,
      totalLocations: locations.filter(loc => loc.isActive).length,
      coveredProvinces: [...new Set(locations.filter(loc => loc.isActive).map(loc => loc.provinsi))].length,
      defaultAdmin: admins.find(admin => admin.isDefault)
    }
  }

  // Initialize on composable creation
  onMounted(() => {
    fetchWhatsAppConfig()
  })

  return {
    // State
    whatsappData: readonly(whatsappData),
    loading: readonly(loading),
    error: readonly(error),
    
    // Methods
    fetchWhatsAppConfig,
    getLocationWhatsApp,
    createApplyWhatsAppUrl,
    createShareWhatsAppUrl,
    applyViaWhatsApp,
    shareViaWhatsApp,
    getAdminStats,
    
    // Computed
    isReady: computed(() => !!whatsappData.value && !loading.value)
  }
} 