// Composable untuk mengelola data jobs
// Ini akan memudahkan migrasi ke Strapi nantinya

export const useJobs = () => {
  // Base URL untuk API (akan diganti dengan Strapi URL nantinya)
  const config = useRuntimeConfig()
  const baseURL = config.public.apiBase || '/api'

  // Fetch all jobs
  const fetchJobs = async (filters = {}) => {
    try {
      // Saat ini menggunakan data lokal
      const { default: jobsData } = await import('~/data/data.json')
      
      let filteredJobs = jobsData
      
      // Apply filters
      if (filters.provinsi) {
        filteredJobs = filteredJobs.filter(job => job.provinsi === filters.provinsi)
      }
      
      if (filters.kabupaten) {
        filteredJobs = filteredJobs.filter(job => job.kabupaten === filters.kabupaten)
      }
      
      if (filters.pendidikan) {
        filteredJobs = filteredJobs.filter(job => job.pendidikan === filters.pendidikan)
      }
      
      if (filters.jenis<PERSON>elamin) {
        filteredJobs = filteredJobs.filter(job => job.jenisKelamin === filters.jenisKelamin)
      }
      
      if (filters.divisi) {
        filteredJobs = filteredJobs.filter(job => job.divisi === filters.divisi)
      }
      
      return {
        data: filteredJobs,
        meta: {
          total: filteredJobs.length,
          page: 1,
          pageSize: filteredJobs.length,
          pageCount: 1
        }
      }
      
      // Nantinya akan diganti dengan:
      // const response = await $fetch(`${baseURL}/jobs`, {
      //   query: filters
      // })
      // return response
      
    } catch (error) {
      console.error('Error fetching jobs:', error)
      throw error
    }
  }

  // Fetch single job by slug
  const fetchJobBySlug = async (slug) => {
    try {
      // Saat ini menggunakan data lokal
      const { default: jobsData } = await import('~/data/data.json')
      const job = jobsData.find(job => job.slug === slug)
      
      if (!job) {
        throw createError({
          statusCode: 404,
          statusMessage: 'Job not found'
        })
      }
      
      return { data: job }
      
      // Nantinya akan diganti dengan:
      // const response = await $fetch(`${baseURL}/jobs`, {
      //   query: {
      //     filters: {
      //       slug: {
      //         $eq: slug
      //       }
      //     }
      //   }
      // })
      // return response
      
    } catch (error) {
      console.error('Error fetching job:', error)
      throw error
    }
  }

  // Get unique values for filters
  const getFilterOptions = async () => {
    try {
      const { data: jobs } = await fetchJobs()
      
      return {
        provinsi: [...new Set(jobs.map(job => job.provinsi))].sort(),
        kabupaten: [...new Set(jobs.map(job => job.kabupaten))].sort(),
        pendidikan: [...new Set(jobs.map(job => job.pendidikan))].sort(),
        jenisKelamin: [...new Set(jobs.map(job => job.jenisKelamin))].sort(),
        divisi: [...new Set(jobs.map(job => job.divisi))].sort(),
        jenis: [...new Set(jobs.map(job => job.jenis))].sort()
      }
    } catch (error) {
      console.error('Error fetching filter options:', error)
      throw error
    }
  }

  // Get statistics
  const getJobStats = async () => {
    try {
      const { data: jobs } = await fetchJobs()
      
      return {
        totalJobs: jobs.length,
        totalProvinsi: [...new Set(jobs.map(job => job.provinsi))].length,
        totalKabupaten: [...new Set(jobs.map(job => job.kabupaten))].length,
        totalPendidikan: [...new Set(jobs.map(job => job.pendidikan))].length,
        totalDivisi: [...new Set(jobs.map(job => job.divisi))].length,
        byJenis: {
          fullTime: jobs.filter(job => job.jenis === 'Full-time').length,
          contract: jobs.filter(job => job.jenis === 'Kontrak').length,
          partTime: jobs.filter(job => job.jenis === 'Part-time').length
        }
      }
    } catch (error) {
      console.error('Error fetching job stats:', error)
      throw error
    }
  }

  return {
    fetchJobs,
    fetchJobBySlug,
    getFilterOptions,
    getJobStats
  }
}