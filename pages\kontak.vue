<template>
  <div>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
        <div class="text-center">
          <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
            Hubungi <span class="text-yellow-300">PT Gemilang</span>
          </h1>
          <p class="text-lg sm:text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
            Kami siap membantu Anda. Hubungi tim kami untuk informasi lebih lanjut tentang layanan dan peluang karir.
          </p>
        </div>
      </div>
      
      <!-- Floating Elements -->
      <div class="absolute top-20 left-4 sm:left-10 w-16 h-16 sm:w-20 sm:h-20 bg-yellow-300 rounded-full opacity-20 animate-pulse"></div>
      <div class="absolute bottom-20 right-4 sm:right-10 w-24 h-24 sm:w-32 sm:h-32 bg-blue-300 rounded-full opacity-20 animate-bounce"></div>
    </section>

    <!-- Contact Information -->
    <section class="py-16 sm:py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- Contact Details -->
          <div>
            <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-8">
              Informasi <span class="text-blue-600">Kontak</span>
            </h2>
            
            <div class="space-y-8">
              <!-- Address -->
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">Alamat Kantor</h3>
                  <p class="text-gray-600 leading-relaxed">
                    Jl. Sukun Mataram Bumi Sejahtera No.3<br>
                    Ngringin, Condongcatur<br>
                    Kec. Depok, Kabupaten Sleman<br>
                    Daerah Istimewa Yogyakarta 55281
                  </p>
                </div>
              </div>

              <!-- Phone -->
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">Telepon</h3>
                  <div class="space-y-1">
                    <p class="text-gray-600">
                      <a href="tel:+622123456789" class="hover:text-blue-600 transition-colors">
                        (*************
                      </a>
                    </p>
                    <p class="text-gray-600">
                      <a href="tel:+6281234567890" class="hover:text-blue-600 transition-colors">
                        +62 812-3456-7890 (WhatsApp)
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              <!-- Email -->
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">Email</h3>
                  <div class="space-y-1">
                    <p class="text-gray-600">
                      <a href="mailto:<EMAIL>" class="hover:text-blue-600 transition-colors">
                        <EMAIL>
                      </a>
                    </p>
                    <p class="text-gray-600">
                      <a href="mailto:<EMAIL>" class="hover:text-blue-600 transition-colors">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              <!-- Business Hours -->
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">Jam Operasional</h3>
                  <div class="space-y-1 text-gray-600">
                    <p>Senin - Jumat: 08:00 - 17:00 WIB</p>
                    <p>Sabtu: 08:00 - 12:00 WIB</p>
                    <p>Minggu: Tutup</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Social Media -->
            <div class="mt-12">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Ikuti Kami</h3>
              <div class="flex space-x-4">
                <a href="#" class="w-10 h-10 bg-blue-600 text-white rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" class="w-10 h-10 bg-blue-800 text-white rounded-lg flex items-center justify-center hover:bg-blue-900 transition-colors">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </a>
                <a href="#" class="w-10 h-10 bg-blue-700 text-white rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="https://wa.me/6281234567890" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-green-600 text-white rounded-lg flex items-center justify-center hover:bg-green-700 transition-colors">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.106"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="bg-gray-50 p-6 sm:p-8 rounded-xl">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Kirim Pesan</h2>
            
            <form @submit.prevent="submitForm" class="space-y-6">
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                    Nama Depan *
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    v-model="form.firstName"
                    required
                    class="input-field"
                    placeholder="Masukkan nama depan"
                  >
                </div>
                <div>
                  <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                    Nama Belakang *
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    v-model="form.lastName"
                    required
                    class="input-field"
                    placeholder="Masukkan nama belakang"
                  >
                </div>
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  id="email"
                  v-model="form.email"
                  required
                  class="input-field"
                  placeholder="<EMAIL>"
                >
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                  Nomor Telepon
                </label>
                <input
                  type="tel"
                  id="phone"
                  v-model="form.phone"
                  class="input-field"
                  placeholder="+62 812-3456-7890"
                >
              </div>

              <div>
                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                  Subjek *
                </label>
                <select
                  id="subject"
                  v-model="form.subject"
                  required
                  class="input-field"
                >
                  <option value="">Pilih subjek</option>
                  <option value="recruitment">Informasi Recruitment</option>
                  <option value="services">Layanan Perusahaan</option>
                  <option value="partnership">Kemitraan</option>
                  <option value="complaint">Keluhan</option>
                  <option value="other">Lainnya</option>
                </select>
              </div>

              <div>
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                  Pesan *
                </label>
                <textarea
                  id="message"
                  v-model="form.message"
                  required
                  rows="5"
                  class="input-field resize-none"
                  placeholder="Tulis pesan Anda di sini..."
                ></textarea>
              </div>

              <div class="flex items-start">
                <input
                  type="checkbox"
                  id="privacy"
                  v-model="form.agreePrivacy"
                  required
                  class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label for="privacy" class="ml-2 text-sm text-gray-600">
                  Saya setuju dengan <a href="#" class="text-blue-600 hover:text-blue-700">kebijakan privasi</a> 
                  dan <a href="#" class="text-blue-600 hover:text-blue-700">syarat & ketentuan</a> *
                </label>
              </div>

              <button
                type="submit"
                :disabled="isSubmitting"
                class="btn-primary w-full text-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="isSubmitting" class="w-5 h-5 mr-2 animate-spin inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                <svg v-else class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                </svg>
                {{ isSubmitting ? 'Mengirim...' : 'Kirim Pesan' }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Map Section -->
    <section class="py-16 sm:py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Lokasi <span class="text-blue-600">Kantor Kami</span>
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            Kunjungi kantor kami di Yogyakarta untuk konsultasi langsung atau informasi lebih lanjut
          </p>
        </div>

        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="aspect-w-16 aspect-h-9 lg:aspect-h-6">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3953.0234567890123!2d110.4110031!3d-7.7573603!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e7a599c11e95f2d%3A0x87d0733d7c91e685!2sTata%20Karya%20Gemilang.%20PT!5e0!3m2!1sen!2sid!4v1234567890123!5m2!1sen!2sid"
              width="100%"
              height="450"
              style="border:0;"
              allowfullscreen=""
              loading="lazy"
              referrerpolicy="no-referrer-when-downgrade"
              class="w-full h-full"
            ></iframe>
          </div>
          
          <div class="p-6 sm:p-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">PT Gemilang - Kantor Pusat</h3>
                <p class="text-gray-600 mb-4 sm:mb-0">
                  Jl. Sukun Mataram Bumi Sejahtera No.3, Ngringin, Condongcatur, Kec. Depok, Kabupaten Sleman, DIY 55281
                </p>
              </div>
              <div class="flex space-x-3">
                <a
                  href="https://www.google.com/maps/place/Tata+Karya+Gemilang.+PT/@-7.7573603,110.4110031,15z/data=!4m6!3m5!1s0x2e7a599c11e95f2d:0x87d0733d7c91e685!8m2!3d-7.7573603!4d110.4110031!16s%2Fg%2F1hc1zydgr?entry=ttu&g_ep=EgoyMDI0MTAwMi4xIKXMDSoASAFQAw%3D%3D"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="btn-primary text-sm"
                >
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  Lihat di Maps
                </a>
                <a
                  href="https://www.google.com/maps/dir//Tata+Karya+Gemilang.+PT/@-7.7573603,110.4110031,15z"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="btn-secondary text-sm"
                >
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                  </svg>
                  Petunjuk Arah
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 sm:py-20 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Pertanyaan <span class="text-blue-600">Umum</span>
          </h2>
          <p class="text-lg sm:text-xl text-gray-600">
            Temukan jawaban untuk pertanyaan yang sering diajukan
          </p>
        </div>

        <div class="space-y-6">
          <div
            v-for="(faq, index) in faqs"
            :key="index"
            class="bg-gray-50 rounded-lg overflow-hidden"
          >
            <button
              @click="toggleFaq(index)"
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-100 transition-colors"
            >
              <span class="font-medium text-gray-900">{{ faq.question }}</span>
              <svg
                :class="{ 'rotate-180': faq.isOpen }"
                class="w-5 h-5 text-gray-500 transform transition-transform"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>
            <div
              v-show="faq.isOpen"
              class="px-6 pb-4 text-gray-600 leading-relaxed"
            >
              {{ faq.answer }}
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 sm:py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-6">
          Siap Bergabung dengan Tim Kami?
        </h2>
        <p class="text-lg sm:text-xl mb-8 text-blue-100">
          Jelajahi peluang karir menarik di PT Gemilang dan wujudkan potensi terbaik Anda
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink to="/lowongan" class="btn-primary bg-yellow-400 hover:bg-yellow-500 text-gray-900 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 w-full sm:w-auto">
            Lihat Lowongan Kerja
          </NuxtLink>
          <a href="https://recruitment.gemilanghebat.com/register" target="_blank" rel="noopener noreferrer" class="text-white border-2 border-white hover:bg-white hover:text-blue-700 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold transition-all duration-300 w-full sm:w-auto text-center">
            Daftar Sekarang
          </a>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// Page meta
definePageMeta({
  title: 'Kontak - PT Gemilang'
})

// Form data
const form = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  subject: '',
  message: '',
  agreePrivacy: false
})

const isSubmitting = ref(false)

// FAQ data
const faqs = ref([
  {
    question: 'Bagaimana cara melamar pekerjaan di PT Gemilang?',
    answer: 'Anda dapat melamar melalui portal recruitment online kami di recruitment.gemilanghebat.com atau melihat lowongan terbaru di website ini. Pastikan untuk melengkapi semua dokumen yang diperlukan.',
    isOpen: false
  },
  {
    question: 'Apa saja layanan yang disediakan PT Gemilang?',
    answer: 'PT Gemilang menyediakan layanan facility services (cleaning service, security, maintenance, landscaping) dan outsourcing services (administrative support, customer service, IT support, HR management).',
    isOpen: false
  },
  {
    question: 'Dimana saja lokasi layanan PT Gemilang?',
    answer: 'Kami melayani lebih dari 50 kota di seluruh Indonesia dengan kantor pusat di Yogyakarta. Untuk informasi detail lokasi layanan, silakan hubungi tim kami.',
    isOpen: false
  },
  {
    question: 'Bagaimana cara menjadi mitra bisnis PT Gemilang?',
    answer: 'Untuk informasi kemitraan, silakan hubungi kami <NAME_EMAIL> atau datang langsung ke kantor kami. Tim business development akan membantu Anda.',
    isOpen: false
  },
  {
    question: 'Apakah PT Gemilang menerima magang atau internship?',
    answer: 'Ya, kami memiliki program magang untuk mahasiswa dari berbagai jurusan. Informasi program magang dapat dilihat di portal recruitment atau hubungi bagian HR.',
    isOpen: false
  }
])

// Methods
const toggleFaq = (index) => {
  faqs.value[index].isOpen = !faqs.value[index].isOpen
}

const submitForm = async () => {
  isSubmitting.value = true
  
  try {
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Show success message
    alert('Terima kasih! Pesan Anda telah berhasil dikirim. Tim kami akan menghubungi Anda segera.')
    
    // Reset form
    form.value = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      agreePrivacy: false
    }
  } catch (error) {
    alert('Maaf, terjadi kesalahan. Silakan coba lagi atau hubungi kami langsung.')
  } finally {
    isSubmitting.value = false
  }
}

// SEO
useHead({
  title: 'Kontak - Hubungi PT Gemilang | JobPortal',
  meta: [
    {
      name: 'description',
      content: 'Hubungi PT Gemilang untuk informasi layanan facility services, outsourcing, dan peluang karir. Kantor pusat di Yogyakarta. Telepon: (*************, Email: <EMAIL>'
    },
    {
      name: 'keywords',
      content: 'kontak PT Gemilang, alamat kantor, telepon, email, facility services, outsourcing, Yogyakarta, recruitment'
    },
    {
      property: 'og:title',
      content: 'Kontak PT Gemilang - Facility Services & Outsourcing'
    },
    {
      property: 'og:description',
      content: 'Hubungi kami untuk informasi layanan dan peluang karir di PT Gemilang. Kantor pusat di Jl. Sukun Mataram Bumi Sejahtera No.3, Yogyakarta.'
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ]
})
</script>