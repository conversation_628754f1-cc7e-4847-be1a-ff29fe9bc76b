<template>
  <div>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
        <div class="text-center">
          <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
            Hubungi <span class="text-yellow-300">Tata Karya Gemilang. PT</span>
          </h1>
          <p class="text-lg sm:text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
            Kami siap membantu Anda. Hubungi tim kami untuk informasi lebih lanjut tentang layanan dan peluang karir.
          </p>
        </div>
      </div>
      
      <!-- Floating Elements -->
      <div class="absolute top-20 left-4 sm:left-10 w-16 h-16 sm:w-20 sm:h-20 bg-yellow-300 rounded-full opacity-20 animate-pulse"></div>
      <div class="absolute bottom-20 right-4 sm:right-10 w-24 h-24 sm:w-32 sm:h-32 bg-blue-300 rounded-full opacity-20 animate-bounce"></div>
    </section>

    <!-- Contact Information -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Informasi Kontak</h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Hubungi kami melalui berbagai cara berikut untuk mendapatkan informasi yang Anda butuhkan
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Address -->
          <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Alamat Kantor</h3>
            <p class="text-gray-600 leading-relaxed">
              Jl. Sukun Mataram Bumi Sejahtera No.3, Ngringin, Condongcatur, Kec. Depok, Kabupaten Sleman, Daerah Istimewa Yogyakarta 55281
            </p>
          </div>

          <!-- Phone -->
          <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Telepon</h3>
            <p class="text-gray-600">
              +62 8777-7773-3566
            </p>
            <p class="text-sm text-gray-500 mt-2">Senin - Minggu, 08:00 - 17:00 WIB</p>
          </div>

          <!-- Email -->
          <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Email</h3>
            <p class="text-gray-600">
              <EMAIL>
            </p>
            <p class="text-sm text-gray-500 mt-2">Respon dalam 24 jam</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Buttons -->
    <section class="py-16 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Hubungi Kami Sekarang</h2>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="https://wa.me/6287777733566" target="_blank" class="inline-flex items-center justify-center px-8 py-4 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg transition-colors duration-300">
            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.106"/>
            </svg>
            WhatsApp
          </a>
          <a href="mailto:<EMAIL>" class="inline-flex items-center justify-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-300">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Email
          </a>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Pertanyaan Umum</h2>
          <p class="text-lg text-gray-600">
            Temukan jawaban untuk pertanyaan yang sering diajukan
          </p>
        </div>

        <div class="space-y-4">
          <div v-for="(faq, index) in simpleFaqs" :key="index" class="bg-white rounded-lg shadow-md">
            <button @click="toggleFaq(index)" class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-200">
              <span class="font-semibold text-gray-900">{{ faq.question }}</span>
              <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" :class="{ 'rotate-180': faq.isOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div v-show="faq.isOpen" class="px-6 pb-4">
              <p class="text-gray-600 leading-relaxed">{{ faq.answer }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-blue-600">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-white mb-4">Siap Bergabung dengan Tim Kami?</h2>
        <p class="text-xl text-blue-100 mb-8">
          Jelajahi peluang karir menarik di Tata Karya Gemilang. PT dan wujudkan potensi terbaik Anda
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink to="/lowongan" class="inline-flex items-center justify-center px-8 py-4 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg transition-colors duration-300">
            Lihat Lowongan Kerja
          </NuxtLink>
          <NuxtLink to="/recruitment" class="inline-flex items-center justify-center px-8 py-4 bg-white hover:bg-gray-100 text-blue-600 font-semibold rounded-lg transition-colors duration-300">
            Portal Recruitment
          </NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// Page meta
definePageMeta({
  title: 'Kontak - Tata Karya Gemilang. PT'
})

// Simple FAQ data
const simpleFaqs = ref([
  {
    question: 'Bagaimana cara melamar pekerjaan di Tata Karya Gemilang. PT?',
    answer: 'Anda dapat melamar melalui portal recruitment online kami di recruitment.gemilanghebat.com atau melihat lowongan terbaru di website ini. Pastikan untuk melengkapi semua dokumen yang diperlukan.',
    isOpen: false
  },
  {
    question: 'Apa saja layanan yang disediakan Tata Karya Gemilang. PT?',
    answer: 'Tata Karya Gemilang. PT menyediakan layanan facility services (cleaning service, security, maintenance, landscaping) dan outsourcing services (administrative support, customer service, IT support, HR management).',
    isOpen: false
  },
  {
    question: 'Dimana saja lokasi layanan Tata Karya Gemilang. PT?',
    answer: 'Kami melayani lebih dari 50 kota di seluruh Indonesia dengan kantor pusat di Yogyakarta. Untuk informasi detail lokasi layanan, silakan hubungi tim kami.',
    isOpen: false
  },
  {
    question: 'Bagaimana cara menjadi mitra bisnis Tata Karya Gemilang. PT?',
    answer: 'Untuk informasi kemitraan, silakan hubungi kami <NAME_EMAIL> atau datang langsung ke kantor kami. Tim business development akan membantu Anda.',
    isOpen: false
  },
  {
    question: 'Apakah Tata Karya Gemilang. PT menerima magang atau internship?',
    answer: 'Ya, kami memiliki program magang untuk mahasiswa dari berbagai jurusan. Informasi program magang dapat dilihat di portal recruitment atau hubungi bagian HR.',
    isOpen: false
  }
])

// Methods
const toggleFaq = (index) => {
  simpleFaqs.value[index].isOpen = !simpleFaqs.value[index].isOpen
}

// Basic SEO
useHead({
  title: 'Kontak - Tata Karya Gemilang. PT',
  meta: [
    { name: 'description', content: 'Hubungi Tata Karya Gemilang. PT untuk informasi layanan facility services, outsourcing, dan peluang karir. Kantor pusat di Yogyakarta.' },
    { name: 'keywords', content: 'kontak Tata Karya Gemilang. PT, alamat kantor, telepon, email, facility services, outsourcing, Yogyakarta, recruitment' }
  ]
})
</script>
