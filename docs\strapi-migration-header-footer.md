# Header & Footer Strapi Migration Guide

## Overview
Header dan Footer telah direfactor menjadi komponen terpisah dengan data JSON yang Strapi-friendly. Struktur ini memudahkan migrasi ke Strapi CMS dan memungkinkan admin dashboard untuk mengelola konten header dan footer.

## File Structure

### Data Files
- `data/header.json` - Data header (logo, navigasi, styling)
- `data/footer.json` - Data footer (company info, links, contact)

### Type Definitions
- `types/header.ts` - TypeScript interfaces untuk header
- `types/footer.ts` - TypeScript interfaces untuk footer

### Composables
- `composables/useHeader.js` - Logic untuk header data management
- `composables/useFooter.js` - Logic untuk footer data management

### Components
- `components/AppHeader.vue` - Header component
- `components/AppFooter.vue` - Footer component

### Updated Files
- `app.vue` - Simplified layout using new components

## Strapi Content Types

### Header Content Type
```json
{
  "kind": "singleType",
  "collectionName": "headers",
  "info": {
    "singularName": "header",
    "pluralName": "headers",
    "displayName": "Header"
  },
  "attributes": {
    "logo": {
      "type": "component",
      "component": "layout.logo"
    },
    "navigation": {
      "type": "component", 
      "component": "layout.navigation"
    },
    "styling": {
      "type": "component",
      "component": "layout.styling"
    },
    "icons": {
      "type": "component",
      "component": "layout.icons"
    },
    "seo": {
      "type": "component",
      "component": "layout.seo"
    }
  }
}
```

### Footer Content Type
```json
{
  "kind": "singleType",
  "collectionName": "footers",
  "info": {
    "singularName": "footer",
    "pluralName": "footers",
    "displayName": "Footer"
  },
  "attributes": {
    "company": {
      "type": "component",
      "component": "layout.company"
    },
    "socialMedia": {
      "type": "component",
      "component": "layout.social-media",
      "repeatable": true
    },
    "sections": {
      "type": "component",
      "component": "layout.footer-section",
      "repeatable": true
    },
    "contact": {
      "type": "component",
      "component": "layout.contact"
    },
    "copyright": {
      "type": "component",
      "component": "layout.copyright"
    },
    "styling": {
      "type": "component",
      "component": "layout.footer-styling"
    }
  }
}
```

## Migration Steps

### 1. Create Strapi Components
Create the following components in Strapi:

#### layout.logo
- src (Text)
- alt (Text)
- link (Text)
- height (Text)
- width (Text)

#### layout.navigation-item
- id (Text)
- text (Text)
- link (Text)
- style (Text)

#### layout.navigation
- desktop (Component: navigation-item, repeatable)
- mobile (Component: navigation-item, repeatable)

#### layout.company
- name (Text)
- description (Text)

#### layout.social-media
- id (Text)
- name (Text)
- link (Text)
- icon (JSON)

#### layout.footer-section-item
- text (Text)
- link (Text)
- type (Enumeration: internal, external)

#### layout.footer-section
- id (Text)
- title (Text)
- items (Component: footer-section-item, repeatable)

#### layout.contact
- title (Text)
- email (Email)
- phone (Text)
- address (Text)

### 2. Update Composables
Replace JSON imports with Strapi API calls:

```javascript
// In useHeader.js
const fetchHeaderData = async () => {
  try {
    const response = await $fetch('/api/header')
    headerData.value = response.data.attributes
  } catch (err) {
    // Handle error
  }
}

// In useFooter.js
const fetchFooterData = async () => {
  try {
    const response = await $fetch('/api/footer')
    footerData.value = response.data.attributes
  } catch (err) {
    // Handle error
  }
}
```

### 3. Create API Routes
Create Nuxt API routes for Strapi integration:

#### `server/api/header.get.js`
```javascript
export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()
  
  try {
    const response = await $fetch(`${config.public.strapiUrl}/api/header?populate=deep`, {
      headers: {
        'Authorization': `Bearer ${config.strapiToken}`
      }
    })
    
    return response
  } catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch header data'
    })
  }
})
```

#### `server/api/footer.get.js`
```javascript
export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()
  
  try {
    const response = await $fetch(`${config.public.strapiUrl}/api/footer?populate=deep`, {
      headers: {
        'Authorization': `Bearer ${config.strapiToken}`
      }
    })
    
    return response
  } catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch footer data'
    })
  }
})
```

### 4. Environment Configuration
Add Strapi configuration to `nuxt.config.ts`:

```typescript
export default defineNuxtConfig({
  runtimeConfig: {
    strapiToken: process.env.STRAPI_TOKEN,
    public: {
      strapiUrl: process.env.STRAPI_URL || 'http://localhost:1337'
    }
  }
})
```

## Benefits

### For Developers
- **Modular Structure**: Header dan footer terpisah dari app.vue
- **Type Safety**: TypeScript interfaces untuk data validation
- **Reusable Logic**: Composables dapat digunakan di komponen lain
- **Easy Testing**: Komponen dapat ditest secara terpisah

### For Content Managers
- **Admin Dashboard**: Dapat mengedit header/footer melalui Strapi admin
- **Real-time Updates**: Perubahan langsung terlihat di website
- **No Code Changes**: Tidak perlu developer untuk update konten
- **Version Control**: Strapi menyediakan draft/publish workflow

### For SEO
- **Dynamic Meta Tags**: SEO data dapat dikelola melalui CMS
- **Structured Data**: Konsisten dengan format JSON-LD
- **Performance**: Data di-cache dan di-optimize oleh Strapi

## Current Implementation
Saat ini menggunakan JSON files sebagai data source. Untuk migrasi ke Strapi:
1. Import data JSON ke Strapi content types
2. Update composables untuk menggunakan API calls
3. Test semua functionality
4. Deploy dengan environment variables yang sesuai

## Fallback Strategy
Jika Strapi tidak tersedia, aplikasi akan fallback ke JSON files yang ada, memastikan website tetap berfungsi.
