// Footer Types
export interface FooterCompany {
  name: string
  description: string
}

export interface FooterIcon {
  viewBox: string
  path: string
}

export interface FooterSocialMedia {
  id: string
  name: string
  link: string
  icon: FooterIcon
}

export interface FooterSectionItem {
  text: string
  link: string
  type: 'internal' | 'external'
}

export interface FooterSection {
  id: string
  title: string
  items: FooterSectionItem[]
}

export interface FooterContact {
  title: string
  email: string
  phone: string
  address: string
}

export interface FooterCopyright {
  text: string
}

export interface FooterStyling {
  footer: string
  container: string
  grid: string
  companySection: string
  sectionTitle: string
  sectionList: string
  linkHover: string
  socialIcons: string
  socialIcon: string
  copyright: string
}

export interface FooterData {
  company: FooterCompany
  socialMedia: FooterSocialMedia[]
  sections: FooterSection[]
  contact: FooterContact
  copyright: FooterCopyright
  styling: FooterStyling
}

// Strapi Response Types (for future use)
export interface StrapiFooterResponse {
  data: {
    id: number
    attributes: FooterData
  }
  meta: object
}

export interface StrapiFooterListResponse {
  data: StrapiFooterResponse[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}
