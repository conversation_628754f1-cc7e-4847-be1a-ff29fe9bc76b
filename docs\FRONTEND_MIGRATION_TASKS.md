# 🎯 Frontend Migration Tasks - Strapi Integration

## 📋 **OVERVIEW**

Daftar task spesifik untuk frontend team dalam migrasi ke Strapi. Hanya 5 files yang perlu diupdate + 1 composable.

---

## ✅ **FILES YANG PERLU DIUPDATE**

### **1. pages/index.vue**

**Current (Line 352):**
```javascript
const { default: jobsData } = await import('~/data/data.json')
const allJobs = ref(jobsData)
```

**Update to:**
```javascript
const { fetchJobs } = useJobs()
const { data: jobsData } = await fetchJobs()
const allJobs = ref(jobsData)
```

### **2. pages/lowongan/index.vue**

**Current (Line 135):**
```javascript
const { default: jobsData } = await import('~/data/data.json')
const allJobs = ref(jobsData)
```

**Update to:**
```javascript
const { fetchJobs } = useJobs()
const { data: jobsData } = await fetchJobs()
const allJobs = ref(jobsData)
```

### **3. pages/lokasi/[provinsi]/index.vue**

**Current (Lines 114 & 140):**
```javascript
// In definePageMeta validation
const { default: jobsData } = await import('~/data/data.json')

// In script setup
const { default: jobsData } = await import('~/data/data.json')
```

**Update to:**
```javascript
// In definePageMeta validation
const { fetchJobs } = useJobs()
const { data: jobsData } = await fetchJobs()

// In script setup
const { fetchJobs } = useJobs()
const { data: jobsData } = await fetchJobs()
```

### **4. pages/lokasi/[provinsi]/[kota].vue**

**Current (Lines 104 & 134):**
```javascript
// In definePageMeta validation
const { default: jobsData } = await import('~/data/data.json')

// In script setup
const { default: jobsData } = await import('~/data/data.json')
```

**Update to:**
```javascript
// In definePageMeta validation
const { fetchJobs } = useJobs()
const { data: jobsData } = await fetchJobs()

// In script setup
const { fetchJobs } = useJobs()
const { data: jobsData } = await fetchJobs()
```

### **5. pages/lowongan/[slug].vue**

**Current (Line 239):**
```javascript
const { default: jobsData } = await import('~/data/data.json')
const job = jobsData.find(job => job.slug === slug)
```

**Update to:**
```javascript
const { fetchJobBySlug } = useJobs()
const { data: job } = await fetchJobBySlug(slug)
```

### **6. composables/useJobs.js** (MAJOR UPDATE)

**Current implementation menggunakan local data, update ke Strapi API:**

```javascript
// Fetch all jobs
const fetchJobs = async (filters = {}) => {
  try {
    const config = useRuntimeConfig()
    const response = await $fetch(`${config.public.strapiUrl}/jobs`, {
      query: {
        populate: '*',
        filters: buildStrapiFilters(filters),
        pagination: {
          page: filters.page || 1,
          pageSize: filters.pageSize || 25
        }
      }
    })
    
    return {
      data: response.data.map(transformStrapiJob),
      meta: response.meta.pagination
    }
  } catch (error) {
    console.error('Error fetching jobs:', error)
    throw error
  }
}

// Fetch single job by slug
const fetchJobBySlug = async (slug) => {
  try {
    const config = useRuntimeConfig()
    const response = await $fetch(`${config.public.strapiUrl}/jobs`, {
      query: {
        populate: '*',
        filters: {
          slug: {
            $eq: slug
          }
        }
      }
    })
    
    if (!response.data || response.data.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Job not found'
      })
    }
    
    return { data: transformStrapiJob(response.data[0]) }
  } catch (error) {
    console.error('Error fetching job:', error)
    throw error
  }
}

// Helper function to build Strapi filters
const buildStrapiFilters = (filters) => {
  const strapiFilters = {}
  
  if (filters.provinsi) {
    strapiFilters.provinsi = { $eq: filters.provinsi }
  }
  
  if (filters.kabupaten) {
    strapiFilters.kabupaten = { $eq: filters.kabupaten }
  }
  
  if (filters.pendidikan) {
    strapiFilters.pendidikan = { $eq: filters.pendidikan }
  }
  
  if (filters.jenisKelamin) {
    strapiFilters.jenisKelamin = { $eq: filters.jenisKelamin }
  }
  
  if (filters.divisi) {
    strapiFilters.divisi = { $eq: filters.divisi }
  }
  
  if (filters.search) {
    strapiFilters.$or = [
      { judul: { $containsi: filters.search } },
      { deskripsi: { $containsi: filters.search } },
      { divisi: { $containsi: filters.search } }
    ]
  }
  
  return strapiFilters
}
```

---

## 🔧 **ENVIRONMENT CONFIGURATION**

### **Update .env file:**

```bash
# Add these variables
STRAPI_URL=http://localhost:1337/api
STRAPI_TOKEN=your-strapi-api-token

# For production
STRAPI_URL=https://your-strapi-domain.com/api
STRAPI_TOKEN=your-production-api-token
```

### **Verify nuxt.config.ts:**

```javascript
runtimeConfig: {
  strapiToken: process.env.STRAPI_TOKEN,
  public: {
    strapiUrl: process.env.STRAPI_URL || 'http://localhost:1337/api'
  }
}
```

---

## 🧪 **TESTING CHECKLIST**

### **After Updates, Test:**

1. **Homepage:**
   - [ ] Job statistics display correctly
   - [ ] Job categories show proper counts
   - [ ] Featured jobs load

2. **Job Listing Page:**
   - [ ] All jobs display
   - [ ] Filters work (provinsi, kabupaten, pendidikan, etc.)
   - [ ] Pagination works
   - [ ] Search functionality

3. **Location Pages:**
   - [ ] Province pages load with correct jobs
   - [ ] City pages show location-specific jobs
   - [ ] Statistics are accurate

4. **Job Detail Pages:**
   - [ ] Individual job pages load
   - [ ] All job information displays
   - [ ] WhatsApp dynamic system still works
   - [ ] Related jobs show correctly

5. **Error Handling:**
   - [ ] 404 pages for non-existent jobs
   - [ ] Error states for API failures
   - [ ] Loading states work properly

---

## 🚨 **IMPORTANT NOTES**

### **WhatsApp System:**
- ✅ **NO CHANGES NEEDED** - WhatsApp dynamic system tidak terpengaruh
- ✅ File `config/whatsapp.js` tetap sama
- ✅ Composable `useWhatsApp.js` tetap sama

### **Components:**
- ✅ **NO CHANGES NEEDED** - Semua components tetap sama
- ✅ JobCard.vue tidak perlu diubah
- ✅ SearchBar dan filter components tetap sama

### **Styling:**
- ✅ **NO CHANGES NEEDED** - CSS dan Tailwind tetap sama

### **Routes:**
- ✅ **NO CHANGES NEEDED** - URL structure tetap sama

---

## ⏱️ **ESTIMATED TIME**

| Task | Time | Complexity |
|------|------|------------|
| Update 5 page files | 1 hour | Low |
| Update useJobs composable | 2 hours | Medium |
| Environment configuration | 30 min | Low |
| Testing all features | 1 hour | Low |
| **TOTAL** | **4.5 hours** | **Low-Medium** |

---

## 🎯 **STEP-BY-STEP EXECUTION**

### **Phase 1: Preparation (30 min)**
1. Backup current code
2. Create feature branch: `git checkout -b strapi-integration`
3. Update environment variables
4. Verify Strapi backend is running

### **Phase 2: Code Updates (3 hours)**
1. Update `composables/useJobs.js` first
2. Update page files one by one
3. Test each file after update
4. Fix any TypeScript errors

### **Phase 3: Testing (1 hour)**
1. Test all pages and features
2. Verify WhatsApp system still works
3. Check error handling
4. Test with different data scenarios

### **Phase 4: Deployment (30 min)**
1. Update production environment variables
2. Deploy to staging for final testing
3. Deploy to production
4. Monitor for any issues

---

## 🎉 **SUCCESS CRITERIA**

✅ All pages load without errors
✅ All filters and search work
✅ WhatsApp dynamic system functions
✅ Performance is maintained
✅ SEO meta tags still work
✅ Error handling works properly

**Result: Zero breaking changes for users!**
