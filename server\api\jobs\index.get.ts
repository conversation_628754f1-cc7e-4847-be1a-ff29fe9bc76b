// API route untuk jobs (akan memudahkan testing sebelum migrasi ke Strapi)

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  
  try {
    // Import data lokal
    const jobsData = await import('~/data/data.json').then(m => m.default)
    
    let filteredJobs = jobsData
    
    // Apply filters
    if (query.provinsi) {
      filteredJobs = filteredJobs.filter(job => job.provinsi === query.provinsi)
    }
    
    if (query.kabupaten) {
      filteredJobs = filteredJobs.filter(job => job.kabupaten === query.kabupaten)
    }
    
    if (query.pendidikan) {
      filteredJobs = filteredJobs.filter(job => job.pendidikan === query.pendidikan)
    }
    
    if (query.jenisKelamin) {
      filteredJobs = filteredJobs.filter(job => job.jenisKelamin === query.jenisKelamin)
    }
    
    if (query.divisi) {
      filteredJobs = filteredJobs.filter(job => job.divisi === query.divisi)
    }
    
    if (query.search) {
      const searchTerm = query.search.toString().toLowerCase()
      filteredJobs = filteredJobs.filter(job => 
        job.judul.toLowerCase().includes(searchTerm) ||
        job.deskripsi.toLowerCase().includes(searchTerm) ||
        job.divisi.toLowerCase().includes(searchTerm)
      )
    }
    
    // Pagination
    const page = parseInt(query.page as string) || 1
    const pageSize = parseInt(query.pageSize as string) || 10
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    
    const paginatedJobs = filteredJobs.slice(startIndex, endIndex)
    
    return {
      data: paginatedJobs,
      meta: {
        total: filteredJobs.length,
        page,
        pageSize,
        pageCount: Math.ceil(filteredJobs.length / pageSize)
      }
    }
  } catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch jobs'
    })
  }
})