<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <NuxtLink to="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
            Beranda
          </NuxtLink>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ decodedCategory }}</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Page Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Lowongan {{ decodedCategory }}</h1>
      <p class="text-lg text-gray-600">
        Temukan berbagai lowongan pekerjaan di bidang {{ decodedCategory }} yang sesuai dengan keahlian dan minat Anda
      </p>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Filter Pencarian</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Location Filter -->
        <div>
          <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Lokasi</label>
          <select 
            id="location" 
            v-model="selectedLocation"
            class="w-full rounded-md border border-gray-300 py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Semua Lokasi</option>
            <option v-for="location in uniqueLocations" :key="location" :value="location">
              {{ location }}
            </option>
          </select>
        </div>
        
        <!-- Education Filter -->
        <div>
          <label for="education" class="block text-sm font-medium text-gray-700 mb-1">Pendidikan</label>
          <select 
            id="education" 
            v-model="selectedEducation"
            class="w-full rounded-md border border-gray-300 py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Semua Pendidikan</option>
            <option v-for="education in uniqueEducations" :key="education" :value="education">
              {{ education }}
            </option>
          </select>
        </div>
        
        <!-- Job Type Filter -->
        <div>
          <label for="jobType" class="block text-sm font-medium text-gray-700 mb-1">Jenis Pekerjaan</label>
          <select 
            id="jobType" 
            v-model="selectedJobType"
            class="w-full rounded-md border border-gray-300 py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Semua Jenis</option>
            <option v-for="jobType in uniqueJobTypes" :key="jobType" :value="jobType">
              {{ jobType }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Job Listings -->
    <div v-if="filteredJobs.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <JobCard
        v-for="job in filteredJobs"
        :key="job.id"
        :judul="job.judul"
        :provinsi="job.provinsi"
        :kabupaten="job.kabupaten"
        :deadline="job.deadline"
        :jenis="job.jenis"
        :divisi="job.divisi"
        :slug="job.slug"
        :pendidikan="job.pendidikan"
        :jenisKelamin="job.jenisKelamin"
        :gambar="job.gambar"
        :lokasi="job.lokasi"
      />
    </div>
    
    <!-- No Results -->
    <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Tidak ada lowongan ditemukan</h3>
      <p class="text-gray-600 mb-4">
        Tidak ada lowongan yang sesuai dengan filter yang Anda pilih. Coba ubah filter atau kembali lagi nanti.
      </p>
      <button 
        @click="resetFilters" 
        class="btn-secondary"
      >
        Reset Filter
      </button>
    </div>
  </div>
</template>

<script setup>
// Get route params
const route = useRoute()
const category = route.params.category

// Decode the category from URL
const decodedCategory = decodeURIComponent(category)

// Import data
const { default: jobsData } = await import('~/data/data.json')

// Filter jobs by category
const categoryJobs = computed(() => {
  return jobsData.filter(job => job.divisi === decodedCategory)
})

// Filters
const selectedLocation = ref('')
const selectedEducation = ref('')
const selectedJobType = ref('')

// Unique filter options
const uniqueLocations = computed(() => {
  const locations = categoryJobs.value.map(job => `${job.provinsi}, ${job.kabupaten}`)
  return [...new Set(locations)]
})

const uniqueEducations = computed(() => {
  const educations = categoryJobs.value.map(job => job.pendidikan)
  return [...new Set(educations)]
})

const uniqueJobTypes = computed(() => {
  const jobTypes = categoryJobs.value.map(job => job.jenis)
  return [...new Set(jobTypes)]
})

// Filtered jobs
const filteredJobs = computed(() => {
  let filtered = [...categoryJobs.value]
  
  if (selectedLocation.value) {
    filtered = filtered.filter(job =>
      `${job.provinsi}, ${job.kabupaten}` === selectedLocation.value
    )
  }
  
  if (selectedEducation.value) {
    filtered = filtered.filter(job => job.pendidikan === selectedEducation.value)
  }
  
  if (selectedJobType.value) {
    filtered = filtered.filter(job => job.jenis === selectedJobType.value)
  }
  
  return filtered
})

// Reset filters
const resetFilters = () => {
  selectedLocation.value = ''
  selectedEducation.value = ''
  selectedJobType.value = ''
}

// SEO
useHead({
  title: `Lowongan ${decodedCategory} - JobPortal`,
  link: [
    {
      rel: 'canonical',
      href: `https://lokergemilang.com/kategori/${encodeURIComponent(decodedCategory)}`
    }
  ],
  meta: [
    {
      name: 'description',
      content: `Temukan berbagai lowongan pekerjaan di bidang ${decodedCategory} yang sesuai dengan keahlian dan minat Anda. Lamar sekarang juga!`
    },
    {
      property: 'og:title',
      content: `Lowongan ${decodedCategory} - JobPortal`
    },
    {
      property: 'og:description',
      content: `Temukan berbagai lowongan pekerjaan di bidang ${decodedCategory} yang sesuai dengan keahlian dan minat Anda. Lamar sekarang juga!`
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ]
})
</script>