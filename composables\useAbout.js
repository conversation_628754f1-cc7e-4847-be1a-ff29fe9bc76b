// About composable - Strapi-ready architecture
// Mengikuti pattern yang sama dengan useJobs() dan useBlog() untuk konsistensi

import { ref } from 'vue'
// Types akan didefinisikan di ~/types/about.ts untuk TypeScript support

export const useAbout = () => {
  // State management
  const aboutData = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Fetch about data
  const fetchAboutData = async () => {
    loading.value = true
    error.value = null
    
    try {
      // Current implementation: local data
      // Future: akan diganti dengan Strapi API call
      const { default: data } = await import('~/data/about.json')
      aboutData.value = data
      
      return { data }
    } catch (err) {
      error.value = err.message || 'Error loading about data'
      console.error('Error fetching about data:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Get hero section data
  const getHeroData = async () => {
    try {
      if (!aboutData.value) {
        await fetchAboutData()
      }
      return { data: aboutData.value.hero }
    } catch (err) {
      console.error('Error getting hero data:', err)
      throw err
    }
  }

  // Get company overview data
  const getCompanyOverview = async () => {
    try {
      if (!aboutData.value) {
        await fetchAboutData()
      }
      return { data: aboutData.value.companyOverview }
    } catch (err) {
      console.error('Error getting company overview:', err)
      throw err
    }
  }

  // Get services data
  const getServices = async () => {
    try {
      if (!aboutData.value) {
        await fetchAboutData()
      }
      return { data: aboutData.value.services }
    } catch (err) {
      console.error('Error getting services data:', err)
      throw err
    }
  }

  // Get company stats
  const getStats = async () => {
    try {
      if (!aboutData.value) {
        await fetchAboutData()
      }
      return { data: aboutData.value.stats }
    } catch (err) {
      console.error('Error getting stats data:', err)
      throw err
    }
  }

  // Get company values
  const getValues = async () => {
    try {
      if (!aboutData.value) {
        await fetchAboutData()
      }
      return { data: aboutData.value.values }
    } catch (err) {
      console.error('Error getting values data:', err)
      throw err
    }
  }

  // Get team data
  const getTeam = async () => {
    try {
      if (!aboutData.value) {
        await fetchAboutData()
      }
      return { data: aboutData.value.team }
    } catch (err) {
      console.error('Error getting team data:', err)
      throw err
    }
  }

  // Get CTA data
  const getCTA = async () => {
    try {
      if (!aboutData.value) {
        await fetchAboutData()
      }
      return { data: aboutData.value.cta }
    } catch (err) {
      console.error('Error getting CTA data:', err)
      throw err
    }
  }

  // Get SEO data
  const getSEO = async () => {
    try {
      if (!aboutData.value) {
        await fetchAboutData()
      }
      return { data: aboutData.value.seo }
    } catch (err) {
      console.error('Error getting SEO data:', err)
      throw err
    }
  }

  // Future Strapi implementation (commented for reference)
  /*
  const fetchAboutDataFromStrapi = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch('/api/about?populate=*', {
        baseURL: process.env.STRAPI_URL || 'http://localhost:1337'
      })
      
      aboutData.value = response.data
      return { data: response.data }
    } catch (err) {
      error.value = err.message
      console.error('Error fetching about data from Strapi:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  */

  return {
    // State
    aboutData,
    loading,
    error,
    
    // Methods
    fetchAboutData,
    getHeroData,
    getCompanyOverview,
    getServices,
    getStats,
    getValues,
    getTeam,
    getCTA,
    getSEO
  }
}
