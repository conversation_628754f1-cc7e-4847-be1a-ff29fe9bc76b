# Environment variables untuk Strapi integration

# Strapi Configuration
STRAPI_URL=http://localhost:1337
STRAPI_TOKEN=your-strapi-api-token-here

# API Configuration
API_BASE_URL=/api
SITE_URL=http://localhost:3000

# Database (untuk Strapi)
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=strapi_jobportal
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=your-password

# Security
APP_KEYS=your-app-keys-here
API_TOKEN_SALT=your-api-token-salt
ADMIN_JWT_SECRET=your-admin-jwt-secret
TRANSFER_TOKEN_SALT=your-transfer-token-salt
JWT_SECRET=your-jwt-secret