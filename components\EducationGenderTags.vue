<template>
  <div class="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Filter Berdasarkan Kriteria</h3>
    
    <!-- Education Tags -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Tingkat Pendidikan</h4>
      <div class="flex flex-wrap gap-2">
        <button
          v-for="pendidikan in popularPendidikan"
          :key="pendidikan.name"
          @click="filterByEducation(pendidikan.name)"
          class="inline-flex items-center px-2 sm:px-3 py-1 sm:py-2 rounded-full text-xs sm:text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors duration-200 cursor-pointer"
        >
          <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
          </svg>
          <span class="truncate">{{ pendidikan.name }}</span>
          <span class="ml-1 sm:ml-2 bg-blue-200 text-blue-800 text-xs px-1 sm:px-2 py-0.5 sm:py-1 rounded-full">
            {{ pendidikan.count }}
          </span>
        </button>
      </div>
    </div>

    <!-- Gender Tags -->
    <div>
      <h4 class="text-sm font-medium text-gray-700 mb-3">Jenis Kelamin</h4>
      <div class="flex flex-wrap gap-2">
        <button
          v-for="gender in popularGender"
          :key="gender.name"
          @click="filterByGender(gender.name)"
          class="inline-flex items-center px-2 sm:px-3 py-1 sm:py-2 rounded-full text-xs sm:text-sm font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors duration-200 cursor-pointer"
        >
          <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
          </svg>
          <span class="truncate">{{ gender.name }}</span>
          <span class="ml-1 sm:ml-2 bg-green-200 text-green-800 text-xs px-1 sm:px-2 py-0.5 sm:py-1 rounded-full">
            {{ gender.count }}
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  jobs: Array
})

const emit = defineEmits(['filterEducation', 'filterGender'])

// Calculate popular education levels with job counts
const popularPendidikan = computed(() => {
  const pendidikanCount = {}
  
  props.jobs.forEach(job => {
    pendidikanCount[job.pendidikan] = (pendidikanCount[job.pendidikan] || 0) + 1
  })
  
  return Object.entries(pendidikanCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => {
      // Custom sort order for education levels
      const order = ['SD', 'SMP', 'SMA/SMK', 'SMK', 'D3', 'S1', 'S2']
      const aIndex = order.findIndex(level => a.name.includes(level))
      const bIndex = order.findIndex(level => b.name.includes(level))
      return aIndex - bIndex
    })
})

// Calculate popular gender requirements with job counts
const popularGender = computed(() => {
  const genderCount = {}
  
  props.jobs.forEach(job => {
    genderCount[job.jenisKelamin] = (genderCount[job.jenisKelamin] || 0) + 1
  })
  
  return Object.entries(genderCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
})

// Methods
const filterByEducation = (pendidikan) => {
  emit('filterEducation', pendidikan)
}

const filterByGender = (gender) => {
  emit('filterGender', gender)
}
</script>