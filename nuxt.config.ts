// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: false },
  modules: ['@nuxtjs/tailwindcss'],
  css: ['~/assets/css/main.css'],
  
  // Runtime config untuk API endpoints
  runtimeConfig: {
    // Private keys (only available on server-side)
    strapiToken: process.env.STRAPI_TOKEN,
    
    // Public keys (exposed to client-side)
    public: {
      apiBase: process.env.API_BASE_URL || '/api',
      strapiUrl: process.env.STRAPI_URL || 'http://localhost:1337/api',
      siteUrl: process.env.SITE_URL || 'http://localhost:3000'
    }
  },

  // TypeScript configuration
  typescript: {
    typeCheck: true
  }
})