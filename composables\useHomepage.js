import { ref, computed } from 'vue'

export const useHomepage = () => {
  // State
  const homepageData = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Fetch homepage data
  const fetchHomepageData = async () => {
    loading.value = true
    error.value = null
    
    try {
      // Current implementation: Load from local JSON
      const { default: data } = await import('~/data/homepage.json')
      homepageData.value = data
      
      // Future Strapi implementation:
      // const response = await $fetch('/api/homepage')
      // homepageData.value = response.data.attributes
      
    } catch (err) {
      error.value = err
      console.error('Error fetching homepage data:', err)
    } finally {
      loading.value = false
    }
  }

  // Get specific sections
  const getHeroData = () => {
    return {
      data: computed(() => homepageData.value?.hero),
      loading: loading.value,
      error: error.value
    }
  }

  const getStatsData = () => {
    return {
      data: computed(() => homepageData.value?.stats),
      loading: loading.value,
      error: error.value
    }
  }

  const getJobCategoriesConfig = () => {
    return {
      data: computed(() => homepageData.value?.jobCategories),
      loading: loading.value,
      error: error.value
    }
  }

  const getFeaturesData = () => {
    return {
      data: computed(() => homepageData.value?.features),
      loading: loading.value,
      error: error.value
    }
  }

  const getHowItWorksData = () => {
    return {
      data: computed(() => homepageData.value?.howItWorks),
      loading: loading.value,
      error: error.value
    }
  }

  const getTestimonialsData = () => {
    return {
      data: computed(() => homepageData.value?.testimonials),
      loading: loading.value,
      error: error.value
    }
  }

  const getBlogConfig = () => {
    return {
      data: computed(() => homepageData.value?.blog),
      loading: loading.value,
      error: error.value
    }
  }

  const getCTAData = () => {
    return {
      data: computed(() => homepageData.value?.cta),
      loading: loading.value,
      error: error.value
    }
  }

  const getSEOData = () => {
    return {
      data: computed(() => homepageData.value?.seo),
      loading: loading.value,
      error: error.value
    }
  }

  // Helper functions
  const isFeatureEnabled = (featureName) => {
    return computed(() => {
      const feature = homepageData.value?.[featureName]
      return feature?.enabled !== false
    })
  }

  const getButtonProps = (button) => {
    const props = {
      to: button.type === 'primary' || button.type === 'secondary' ? button.link : undefined,
      href: button.type === 'external' ? button.link : undefined,
      class: button.style
    }

    if (button.target) props.target = button.target
    if (button.rel) props.rel = button.rel

    return props
  }

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => index < rating)
  }

  // Future Strapi methods (commented for now)
  /*
  const fetchHomepageFromStrapi = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch('/api/homepage', {
        params: {
          populate: '*'
        }
      })
      
      homepageData.value = response.data.attributes
    } catch (err) {
      error.value = err
      console.error('Error fetching homepage from Strapi:', err)
    } finally {
      loading.value = false
    }
  }

  const updateHomepageInStrapi = async (data) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await $fetch('/api/homepage', {
        method: 'PUT',
        body: {
          data: data
        }
      })
      
      homepageData.value = response.data.attributes
      return response
    } catch (err) {
      error.value = err
      console.error('Error updating homepage in Strapi:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  */

  return {
    // State
    homepageData: readonly(homepageData),
    loading: readonly(loading),
    error: readonly(error),
    
    // Methods
    fetchHomepageData,
    
    // Section getters
    getHeroData,
    getStatsData,
    getJobCategoriesConfig,
    getFeaturesData,
    getHowItWorksData,
    getTestimonialsData,
    getBlogConfig,
    getCTAData,
    getSEOData,
    
    // Helpers
    isFeatureEnabled,
    getButtonProps,
    renderStars
    
    // Future Strapi methods
    // fetchHomepageFromStrapi,
    // updateHomepageInStrapi
  }
}
