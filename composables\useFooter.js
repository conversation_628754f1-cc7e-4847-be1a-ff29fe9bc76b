export const useFooter = () => {
  // State management
  const footerData = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Fetch footer data
  const fetchFooterData = async () => {
    loading.value = true
    error.value = null
    
    try {
      // For now, load from JSON file
      // When migrating to Strapi, replace this with API call
      const { default: data } = await import('~/data/footer.json')
      footerData.value = data
      
      // Future Strapi implementation:
      // const response = await $fetch('/api/footer')
      // footerData.value = response.data.attributes
      
    } catch (err) {
      error.value = err
      console.error('Error loading footer data:', err)
    } finally {
      loading.value = false
    }
  }

  // Get company data
  const getCompanyData = () => {
    return footerData.value?.company || {}
  }

  // Get social media links
  const getSocialMediaLinks = () => {
    return footerData.value?.socialMedia || []
  }

  // Get footer sections
  const getFooterSections = () => {
    return footerData.value?.sections || []
  }

  // Get contact information
  const getContactInfo = () => {
    return footerData.value?.contact || {}
  }

  // Get copyright information
  const getCopyrightInfo = () => {
    return footerData.value?.copyright || {}
  }

  // Get styling classes
  const getStyling = () => {
    return footerData.value?.styling || {}
  }

  // Helper function to get link props based on type
  const getLinkProps = (item) => {
    if (item.type === 'external') {
      return {
        href: item.link,
        target: '_blank',
        rel: 'noopener noreferrer'
      }
    } else {
      return {
        to: item.link
      }
    }
  }

  // Helper function to check if link is external
  const isExternalLink = (item) => {
    return item.type === 'external'
  }

  // Helper function to get current year for copyright
  const getCurrentYear = () => {
    return new Date().getFullYear()
  }

  // Helper function to format copyright text with current year
  const getFormattedCopyright = () => {
    const copyright = getCopyrightInfo()
    if (copyright.text) {
      return copyright.text.replace('2025', getCurrentYear().toString())
    }
    return `© ${getCurrentYear()} Tata Karya Gemilang. PT. All rights reserved.`
  }

  return {
    // State
    footerData: readonly(footerData),
    loading: readonly(loading),
    error: readonly(error),
    
    // Methods
    fetchFooterData,
    getCompanyData,
    getSocialMediaLinks,
    getFooterSections,
    getContactInfo,
    getCopyrightInfo,
    getStyling,
    getLinkProps,
    isExternalLink,
    getCurrentYear,
    getFormattedCopyright
  }
}
