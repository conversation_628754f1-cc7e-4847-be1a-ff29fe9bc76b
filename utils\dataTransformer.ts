// Utility untuk transform data antara format lokal dan Strapi

import type { Job } from '~/types/job'

// Transform Strapi response to our Job interface
export const transformStrapiJob = (strapiJob: any): Job => {
  const attributes = strapiJob.attributes || strapiJob
  
  return {
    id: strapiJob.id || attributes.id,
    judul: attributes.judul,
    provinsi: attributes.provinsi,
    kabupaten: attributes.kabupaten,
    deadline: attributes.deadline,
    jenis: attributes.jenis,
    divisi: attributes.divisi,
    slug: attributes.slug,
    deskripsi: attributes.deskripsi,
    persyaratan: attributes.persyaratan || [],
    gaji: attributes.gaji,
    pendidikan: attributes.pendidikan,
    jenisKelamin: attributes.jenisKelamin,
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
    publishedAt: attributes.publishedAt,
    featured: attributes.featured || false,
    status: attributes.status || 'active',
    applicationCount: attributes.applicationCount || 0,
    viewCount: attributes.viewCount || 0,
    companyLogo: attributes.companyLogo?.data?.attributes?.url,
    benefits: attributes.benefits || [],
    workLocation: attributes.workLocation || 'onsite',
    experienceLevel: attributes.experienceLevel || 'entry',
    salaryMin: attributes.salaryMin,
    salaryMax: attributes.salaryMax,
    currency: attributes.currency || 'IDR'
  }
}

// Transform array of Strapi jobs
export const transformStrapiJobs = (strapiResponse: any): Job[] => {
  const jobs = strapiResponse.data || strapiResponse
  
  if (Array.isArray(jobs)) {
    return jobs.map(transformStrapiJob)
  }
  
  return [transformStrapiJob(jobs)]
}

// Transform local job data to Strapi format (for migration)
export const transformToStrapiFormat = (localJob: Job) => {
  return {
    judul: localJob.judul,
    provinsi: localJob.provinsi,
    kabupaten: localJob.kabupaten,
    deadline: localJob.deadline,
    jenis: localJob.jenis,
    divisi: localJob.divisi,
    slug: localJob.slug,
    deskripsi: localJob.deskripsi,
    persyaratan: localJob.persyaratan,
    gaji: localJob.gaji,
    pendidikan: localJob.pendidikan,
    jenisKelamin: localJob.jenisKelamin,
    status: 'active',
    featured: false,
    publishedAt: new Date().toISOString()
  }
}