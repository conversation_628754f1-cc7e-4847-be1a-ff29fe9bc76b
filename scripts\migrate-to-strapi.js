// Script untuk migrasi data dari lokal ke Strapi
// Jalankan dengan: node scripts/migrate-to-strapi.js

import fs from 'fs'
import path from 'path'

const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1337'
const STRAPI_TOKEN = process.env.STRAPI_TOKEN

async function migrateJobs() {
  try {
    // Read local data
    const dataPath = path.join(process.cwd(), 'data', 'data.json')
    const localJobs = JSON.parse(fs.readFileSync(dataPath, 'utf8'))
    
    console.log(`Found ${localJobs.length} jobs to migrate`)
    
    for (const job of localJobs) {
      try {
        // Transform to Strapi format
        const strapiJob = {
          data: {
            judul: job.judul,
            provinsi: job.provinsi,
            kabupaten: job.kabupaten,
            deadline: job.deadline,
            jenis: job.jenis,
            divisi: job.divisi,
            slug: job.slug,
            deskripsi: job.deskripsi,
            persyaratan: job.persyaratan,
            gaji: job.gaji,
            pendidikan: job.pendidikan,
            jenisKelamin: job.jenisKelamin,
            status: 'active',
            featured: false,
            publishedAt: new Date().toISOString()
          }
        }
        
        // Post to Strapi
        const response = await fetch(`${STRAPI_URL}/api/jobs`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${STRAPI_TOKEN}`
          },
          body: JSON.stringify(strapiJob)
        })
        
        if (response.ok) {
          console.log(`✅ Migrated: ${job.judul}`)
        } else {
          console.error(`❌ Failed to migrate: ${job.judul}`, await response.text())
        }
        
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`❌ Error migrating ${job.judul}:`, error)
      }
    }
    
    console.log('Migration completed!')
    
  } catch (error) {
    console.error('Migration failed:', error)
  }
}

// Run migration
migrateJobs()