<template>
  <div class="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 mb-8">
    <h2 class="text-lg font-semibold text-gray-900 mb-4"><PERSON><PERSON> Berdasarkan Kriteria</h2>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">Provinsi</label>
        <select v-model="selectedProvinsi" @change="onProvinsiChange" class="input-field text-sm">
          <option value="">Semua <PERSON>vinsi</option>
          <option v-for="provinsi in availableProvinsi" :key="provinsi" :value="provinsi">
            {{ provinsi }}
          </option>
        </select>
      </div>
      
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">Kabupaten/Kota</label>
        <select v-model="selectedKabupaten" @change="onKabupatenChange" class="input-field text-sm" :disabled="!selectedProvinsi">
          <option value="">Semua Kabupaten</option>
          <option v-for="kabupaten in availableKabupaten" :key="kabupaten" :value="kabupaten">
            {{ kabupaten }}
          </option>
        </select>
      </div>

      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">Pendidikan</label>
        <select v-model="selectedPendidikan" @change="onFilterChange" class="input-field text-sm">
          <option value="">Semua Pendidikan</option>
          <option v-for="pendidikan in availablePendidikan" :key="pendidikan" :value="pendidikan">
            {{ pendidikan }}
          </option>
        </select>
      </div>

      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700">Jenis Kelamin</label>
        <select v-model="selectedGender" @change="onFilterChange" class="input-field text-sm">
          <option value="">Semua</option>
          <option v-for="gender in availableGender" :key="gender" :value="gender">
            {{ gender }}
          </option>
        </select>
      </div>
    </div>

    <div class="flex flex-col sm:flex-row items-end space-y-2 sm:space-y-0 sm:space-x-2 mt-4">
      <button @click="searchJobs" class="btn-primary flex-1 w-full sm:w-auto text-sm">
        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
        Cari
      </button>
      <button @click="resetFilter" class="btn-secondary w-full sm:w-auto text-sm">
        Reset
      </button>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  jobs: Array
})

const emit = defineEmits(['filter'])

const selectedProvinsi = ref('')
const selectedKabupaten = ref('')
const selectedPendidikan = ref('')
const selectedGender = ref('')

const availableProvinsi = computed(() => {
  const provinsi = [...new Set(props.jobs.map(job => job.provinsi))]
  return provinsi.sort()
})

const availableKabupaten = computed(() => {
  if (!selectedProvinsi.value) return []
  const kabupaten = props.jobs
    .filter(job => job.provinsi === selectedProvinsi.value)
    .map(job => job.kabupaten)
  return [...new Set(kabupaten)].sort()
})

const availablePendidikan = computed(() => {
  const pendidikan = [...new Set(props.jobs.map(job => job.pendidikan))]
  return pendidikan.sort((a, b) => {
    const order = ['SD', 'SMP', 'SMA/SMK', 'SMK', 'D3', 'S1', 'S2']
    const aIndex = order.findIndex(level => a.includes(level))
    const bIndex = order.findIndex(level => b.includes(level))
    return aIndex - bIndex
  })
})

const availableGender = computed(() => {
  const gender = [...new Set(props.jobs.map(job => job.jenisKelamin))]
  return gender.sort()
})

const onProvinsiChange = () => {
  selectedKabupaten.value = ''
  onFilterChange()
}

const onKabupatenChange = () => {
  onFilterChange()
}

const onFilterChange = () => {
  searchJobs()
}

const searchJobs = () => {
  if (selectedProvinsi.value && selectedKabupaten.value && !selectedPendidikan.value && !selectedGender.value) {
    // Navigate to location page if only location is selected
    navigateTo(`/lokasi/${encodeURIComponent(selectedProvinsi.value)}/${encodeURIComponent(selectedKabupaten.value)}`)
  } else {
    // Emit filter for current page
    emit('filter', {
      provinsi: selectedProvinsi.value,
      kabupaten: selectedKabupaten.value,
      pendidikan: selectedPendidikan.value,
      jenisKelamin: selectedGender.value
    })
  }
}

const resetFilter = () => {
  selectedProvinsi.value = ''
  selectedKabupaten.value = ''
  selectedPendidikan.value = ''
  selectedGender.value = ''
  emit('filter', { 
    provinsi: '', 
    kabupaten: '', 
    pendidikan: '', 
    jenisKelamin: '' 
  })
}
</script>