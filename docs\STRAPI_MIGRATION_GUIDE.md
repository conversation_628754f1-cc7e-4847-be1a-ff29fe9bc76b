# Panduan Migrasi ke Strapi CMS

## 📋 Persiapan Migrasi

### 1. Struktur Data yang Sudah Disiapkan
- ✅ **Composable `useJobs()`** - Abstraksi untuk data fetching
- ✅ **TypeScript interfaces** - Konsistensi tipe data
- ✅ **API routes** - Testing endpoint sebelum migrasi
- ✅ **Data transformer** - Konversi format data
- ✅ **Migration script** - Otomasi transfer data

### 2. Schema Strapi yang Direkomendasikan

#### Content Type: `Job`
```json
{
  "kind": "collectionType",
  "collectionName": "jobs",
  "info": {
    "singularName": "job",
    "pluralName": "jobs",
    "displayName": "Job",
    "description": "Job listings for the portal"
  },
  "options": {
    "draftAndPublish": true
  },
  "attributes": {
    "judul": {
      "type": "string",
      "required": true
    },
    "slug": {
      "type": "uid",
      "targetField": "judul",
      "required": true
    },
    "deskripsi": {
      "type": "text",
      "required": true
    },
    "persyaratan": {
      "type": "json"
    },
    "gaji": {
      "type": "string"
    },
    "deadline": {
      "type": "date",
      "required": true
    },
    "provinsi": {
      "type": "string",
      "required": true
    },
    "kabupaten": {
      "type": "string",
      "required": true
    },
    "pendidikan": {
      "type": "enumeration",
      "enum": ["SD", "SMP", "SMA/SMK", "SMK", "D3", "S1", "S2", "S3"]
    },
    "jenisKelamin": {
      "type": "enumeration",
      "enum": ["Laki-laki", "Perempuan", "Laki-laki/Perempuan"]
    },
    "jenis": {
      "type": "enumeration",
      "enum": ["Full-time", "Part-time", "Kontrak", "Freelance"]
    },
    "divisi": {
      "type": "string",
      "required": true
    },
    "status": {
      "type": "enumeration",
      "enum": ["active", "inactive", "expired"],
      "default": "active"
    },
    "featured": {
      "type": "boolean",
      "default": false
    },
    "applicationCount": {
      "type": "integer",
      "default": 0
    },
    "viewCount": {
      "type": "integer",
      "default": 0
    },
    "companyLogo": {
      "type": "media",
      "multiple": false,
      "required": false,
      "allowedTypes": ["images"]
    },
    "benefits": {
      "type": "json"
    },
    "workLocation": {
      "type": "enumeration",
      "enum": ["onsite", "remote", "hybrid"],
      "default": "onsite"
    },
    "experienceLevel": {
      "type": "enumeration",
      "enum": ["entry", "mid", "senior"],
      "default": "entry"
    },
    "salaryMin": {
      "type": "integer"
    },
    "salaryMax": {
      "type": "integer"
    },
    "currency": {
      "type": "string",
      "default": "IDR"
    }
  }
}
```

## 🚀 Langkah-langkah Migrasi

### Phase 1: Setup Strapi
1. Install Strapi
```bash
npx create-strapi-app@latest strapi-jobportal
cd strapi-jobportal
npm run develop
```

2. Buat Content Type `Job` sesuai schema di atas
3. Setup API permissions untuk public read access
4. Generate API token untuk migration

### Phase 2: Data Migration
1. Update environment variables
```bash
cp .env.example .env
# Edit .env dengan konfigurasi Strapi
```

2. Jalankan migration script
```bash
node scripts/migrate-to-strapi.js
```

### Phase 3: Update Frontend
1. Update `useJobs()` composable untuk menggunakan Strapi API
2. Test semua fitur dengan data dari Strapi
3. Update deployment configuration

### Phase 4: Production Deployment
1. Deploy Strapi ke production (Railway, Heroku, VPS, dll)
2. Update environment variables di production
3. Run migration script di production
4. Update frontend untuk menggunakan production Strapi URL

## 🔧 Keuntungan Struktur Ini

### ✅ **Smooth Migration**
- Semua data fetching sudah diabstraksi dalam composable
- Tinggal ganti implementasi di `useJobs()`
- Tidak perlu ubah component logic

### ✅ **Type Safety**
- TypeScript interfaces memastikan konsistensi data
- Compile-time error detection
- Better developer experience

### ✅ **Backward Compatibility**
- API routes lokal bisa digunakan sebagai fallback
- Gradual migration possible
- Easy rollback jika ada masalah

### ✅ **Enhanced Features Ready**
- Schema sudah include field untuk fitur advanced
- Featured jobs, view count, application tracking
- Company logos, benefits, work location

### ✅ **SEO & Performance**
- Strapi built-in SEO fields
- Image optimization
- Caching strategies
- API pagination

## 📊 Monitoring & Analytics

Setelah migrasi, Anda bisa menambahkan:
- Job view tracking
- Application analytics
- Popular search terms
- User behavior insights
- Performance monitoring

## 🔒 Security & Permissions

Strapi menyediakan:
- Role-based access control
- API rate limiting
- Content moderation
- Audit logs
- Secure file uploads

## 🎯 Next Steps

1. **Content Management**: Admin bisa CRUD jobs via Strapi admin panel
2. **Advanced Filtering**: Lebih banyak filter options
3. **Search**: Full-text search dengan Elasticsearch
4. **Notifications**: Email alerts untuk job applications
5. **Analytics Dashboard**: Real-time job portal analytics