// Blog composable - Strapi-ready architecture
// Mengikuti pattern yang sama dengan useJobs() untuk konsistensi

import { ref, computed } from 'vue'
import type { BlogPost, BlogFilters, BlogResponse, BlogStats, BlogSidebar } from '~/types/blog'

export const useBlog = () => {
  // State management
  const posts = ref([])
  const currentPost = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Fetch all blog posts
  const fetchPosts = async (filters = {}) => {
    loading.value = true
    error.value = null
    
    try {
      // Current implementation: local data
      // Future: akan diganti dengan Strapi API call
      const { default: blogData } = await import('~/data/blog.json')
      
      let filteredPosts = [...blogData]
      
      // Apply filters
      if (filters.category) {
        filteredPosts = filteredPosts.filter(post => 
          post.category.toLowerCase() === filters.category.toLowerCase()
        )
      }
      
      if (filters.tag) {
        filteredPosts = filteredPosts.filter(post =>
          post.tags.some(tag => tag.toLowerCase().includes(filters.tag.toLowerCase()))
        )
      }
      
      if (filters.author) {
        filteredPosts = filteredPosts.filter(post =>
          post.author.toLowerCase().includes(filters.author.toLowerCase())
        )
      }
      
      if (filters.featured !== undefined) {
        filteredPosts = filteredPosts.filter(post => post.featured === filters.featured)
      }
      
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        filteredPosts = filteredPosts.filter(post =>
          post.title.toLowerCase().includes(searchTerm) ||
          post.excerpt.toLowerCase().includes(searchTerm) ||
          post.content.toLowerCase().includes(searchTerm) ||
          post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        )
      }
      
      // Apply sorting
      if (filters.sort) {
        switch (filters.sort) {
          case 'newest':
            filteredPosts.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
            break
          case 'oldest':
            filteredPosts.sort((a, b) => new Date(a.publishedAt) - new Date(b.publishedAt))
            break
          case 'popular':
            filteredPosts.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0))
            break
          case 'title':
            filteredPosts.sort((a, b) => a.title.localeCompare(b.title))
            break
        }
      } else {
        // Default: newest first
        filteredPosts.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
      }
      
      // Apply pagination
      const page = filters.page || 1
      const pageSize = filters.pageSize || 10
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedPosts = filteredPosts.slice(startIndex, endIndex)
      
      posts.value = paginatedPosts
      
      return {
        data: paginatedPosts,
        meta: {
          total: filteredPosts.length,
          page: page,
          pageSize: pageSize,
          pageCount: Math.ceil(filteredPosts.length / pageSize)
        }
      }
    } catch (err) {
      error.value = err.message
      console.error('Error fetching blog posts:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Fetch single blog post by slug
  const fetchPostBySlug = async (slug) => {
    loading.value = true
    error.value = null
    
    try {
      // Current implementation: local data
      // Future: akan diganti dengan Strapi API call
      const { default: blogData } = await import('~/data/blog.json')
      const post = blogData.find(post => post.slug === slug)
      
      if (!post) {
        throw createError({
          statusCode: 404,
          statusMessage: 'Blog post not found'
        })
      }
      
      currentPost.value = post
      
      return { data: post }
    } catch (err) {
      error.value = err.message
      console.error('Error fetching blog post:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Get featured posts
  const getFeaturedPosts = async (limit = 3) => {
    try {
      const { default: blogData } = await import('~/data/blog.json')
      const featuredPosts = blogData
        .filter(post => post.featured && post.status === 'published')
        .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
        .slice(0, limit)
      
      return { data: featuredPosts }
    } catch (err) {
      console.error('Error fetching featured posts:', err)
      return { data: [] }
    }
  }

  // Get recent posts
  const getRecentPosts = async (limit = 5) => {
    try {
      const { default: blogData } = await import('~/data/blog.json')
      const recentPosts = blogData
        .filter(post => post.status === 'published')
        .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
        .slice(0, limit)
      
      return { data: recentPosts }
    } catch (err) {
      console.error('Error fetching recent posts:', err)
      return { data: [] }
    }
  }

  // Get related posts
  const getRelatedPosts = async (currentPostId, limit = 3) => {
    try {
      const { default: blogData } = await import('~/data/blog.json')
      const currentPost = blogData.find(post => post.id === currentPostId)
      
      if (!currentPost) return { data: [] }
      
      const relatedPosts = blogData
        .filter(post => 
          post.id !== currentPostId && 
          post.status === 'published' &&
          (post.category === currentPost.category ||
           post.tags.some(tag => currentPost.tags.includes(tag)))
        )
        .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
        .slice(0, limit)
      
      return { data: relatedPosts }
    } catch (err) {
      console.error('Error fetching related posts:', err)
      return { data: [] }
    }
  }

  // Get blog categories
  const getCategories = async () => {
    try {
      const { default: blogData } = await import('~/data/blog.json')
      const categories = [...new Set(blogData.map(post => post.category))]
        .map(category => {
          const postCount = blogData.filter(post => post.category === category).length
          return {
            name: category,
            slug: category.toLowerCase().replace(/\s+/g, '-'),
            postCount
          }
        })
        .sort((a, b) => b.postCount - a.postCount)
      
      return { data: categories }
    } catch (err) {
      console.error('Error fetching categories:', err)
      return { data: [] }
    }
  }

  // Get all tags
  const getTags = async () => {
    try {
      const { default: blogData } = await import('~/data/blog.json')
      const allTags = blogData.flatMap(post => post.tags)
      const tagCounts = allTags.reduce((acc, tag) => {
        acc[tag] = (acc[tag] || 0) + 1
        return acc
      }, {})
      
      const tags = Object.entries(tagCounts)
        .map(([tag, count]) => ({ name: tag, count }))
        .sort((a, b) => b.count - a.count)
      
      return { data: tags }
    } catch (err) {
      console.error('Error fetching tags:', err)
      return { data: [] }
    }
  }

  // Get blog statistics
  const getBlogStats = async () => {
    try {
      const { default: blogData } = await import('~/data/blog.json')
      
      const stats = {
        totalPosts: blogData.length,
        totalCategories: [...new Set(blogData.map(post => post.category))].length,
        totalAuthors: <AUTHORS>
        totalViews: blogData.reduce((sum, post) => sum + (post.viewCount || 0), 0),
        byCategory: {},
        byMonth: {}
      }
      
      // Posts by category
      blogData.forEach(post => {
        stats.byCategory[post.category] = (stats.byCategory[post.category] || 0) + 1
      })
      
      // Posts by month
      blogData.forEach(post => {
        const date = new Date(post.publishedAt)
        const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        stats.byMonth[monthYear] = (stats.byMonth[monthYear] || 0) + 1
      })
      
      return { data: stats }
    } catch (err) {
      console.error('Error fetching blog stats:', err)
      return { data: null }
    }
  }

  // Get sidebar data
  const getSidebarData = async () => {
    try {
      const [recentPosts, categories, tags] = await Promise.all([
        getRecentPosts(5),
        getCategories(),
        getTags()
      ])
      
      return {
        data: {
          recentPosts: recentPosts.data,
          categories: categories.data.slice(0, 8),
          tags: tags.data.slice(0, 15).map(tag => tag.name)
        }
      }
    } catch (err) {
      console.error('Error fetching sidebar data:', err)
      return { data: null }
    }
  }

  // Computed properties
  const publishedPosts = computed(() => 
    posts.value.filter(post => post.status === 'published')
  )

  const featuredPosts = computed(() =>
    posts.value.filter(post => post.featured && post.status === 'published')
  )

  return {
    // State
    posts,
    currentPost,
    loading,
    error,
    
    // Computed
    publishedPosts,
    featuredPosts,
    
    // Methods
    fetchPosts,
    fetchPostBySlug,
    getFeaturedPosts,
    getRecentPosts,
    getRelatedPosts,
    getCategories,
    getTags,
    getBlogStats,
    getSidebarData
  }
}

// Future Strapi implementation (commented for reference)
/*
const fetchPosts = async (filters = {}) => {
  try {
    const config = useRuntimeConfig()
    const response = await $fetch(`${config.public.strapiUrl}/blog-posts`, {
      query: {
        populate: '*',
        filters: buildStrapiFilters(filters),
        pagination: {
          page: filters.page || 1,
          pageSize: filters.pageSize || 10
        },
        sort: filters.sort || 'publishedAt:desc'
      }
    })
    
    return {
      data: response.data.map(transformStrapiBlogPost),
      meta: response.meta.pagination
    }
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    throw error
  }
}
*/
