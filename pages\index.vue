<template>
  <div>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-32">
        <div class="text-center">
          <h1 class="text-3xl sm:text-4xl md:text-6xl font-bold mb-6 leading-tight">
            <PERSON><PERSON><PERSON> <span class="text-yellow-300">Tepat</span><br class="hidden sm:block">
            Bersama Gemilang
          </h1>
          <p class="text-lg sm:text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
           Tempat mencari Info Lowongan kerja terbaik yang paling sesuai dengan minat dan kompetensi anda.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <NuxtLink to="/lowongan" class="btn-primary bg-yellow-400 hover:bg-yellow-500 text-gray-900 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 w-full sm:w-auto">
              <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
              Jelajahi Lowongan
            </NuxtLink>
            <a href="#features" class="text-white border-2 border-white hover:bg-white hover:text-blue-700 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold transition-all duration-300 w-full sm:w-auto text-center">
              Pelajari Lebih Lanjut
            </a>
          </div>
        </div>
      </div>
      
      <!-- Floating Elements -->
      <div class="absolute top-20 left-4 sm:left-10 w-16 h-16 sm:w-20 sm:h-20 bg-yellow-300 rounded-full opacity-20 animate-pulse"></div>
      <div class="absolute bottom-20 right-4 sm:right-10 w-24 h-24 sm:w-32 sm:h-32 bg-blue-300 rounded-full opacity-20 animate-bounce"></div>
      <div class="absolute top-1/2 right-1/4 w-12 h-12 sm:w-16 sm:h-16 bg-indigo-300 rounded-full opacity-20 animate-pulse delay-1000"></div>
    </section>

    <!-- Stats Section -->
    <!-- <section class="py-12 sm:py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-8">
          <div class="text-center">
            <div class="text-2xl sm:text-4xl font-bold text-blue-600 mb-2">{{ totalJobs }}+</div>
            <div class="text-gray-600 font-medium text-sm sm:text-base">Lowongan Aktif</div>
          </div>
          <div class="text-center">
            <div class="text-2xl sm:text-4xl font-bold text-green-600 mb-2">100+</div>
            <div class="text-gray-600 font-medium text-sm sm:text-base">Perusahaan Partner</div>
          </div>
          <div class="text-center">
            <div class="text-2xl sm:text-4xl font-bold text-purple-600 mb-2">10K+</div>
            <div class="text-gray-600 font-medium text-sm sm:text-base">Kandidat Terdaftar</div>
          </div>
          <div class="text-center">
            <div class="text-2xl sm:text-4xl font-bold text-orange-600 mb-2">95%</div>
            <div class="text-gray-600 font-medium text-sm sm:text-base">Tingkat Kepuasan</div>
          </div>
        </div>
      </div>
    </section> -->

    <!-- Quick Job Categories Section -->
    <section class="py-12 sm:py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8 sm:mb-12">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Kategori <span class="text-blue-600">Pekerjaan</span>
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            Temukan lowongan kerja berdasarkan divisi dan kategori yang Anda minati
          </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
          <NuxtLink
            v-for="category in jobCategories"
            :key="category.name"
            :to="`/kategori/${encodeURIComponent(category.name)}`"
            class="group bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 p-4 sm:p-6 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-105"
          >
            <div class="text-center">
              <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-blue-700 transition-colors">
                <!-- Checklist icon SVG - hardcoded to ensure consistent display -->
                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="text-sm sm:text-lg font-semibold text-gray-900 mb-2">{{ category.name }}</h3>
              <p class="text-xs sm:text-sm text-gray-600 mb-3">{{ category.count }} lowongan</p>
              <div class="text-blue-600 text-xs sm:text-sm font-medium group-hover:text-blue-700">
                Lihat Semua →
              </div>
            </div>
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <!-- <section id="features" class="py-16 sm:py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 sm:mb-16">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Mengapa Memilih <span class="text-blue-600">JobPortal</span>?
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            Kami berkomitmen memberikan pengalaman terbaik dalam pencarian kerja dengan fitur-fitur unggulan
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          <div class="text-center p-6 sm:p-8 rounded-xl hover:shadow-lg transition-all duration-300 group">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-blue-200 transition-colors">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
            <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Pencarian Cerdas</h3>
            <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
              Sistem pencarian canggih dengan filter lokasi, pendidikan, dan kriteria lainnya untuk menemukan pekerjaan yang tepat sesuai kualifikasi Anda.
            </p>
          </div>

          <div class="text-center p-6 sm:p-8 rounded-xl hover:shadow-lg transition-all duration-300 group">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-green-200 transition-colors">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Perusahaan Terpercaya</h3>
            <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
              Bermitra dengan perusahaan-perusahaan terkemuka dan startup inovatif yang menawarkan lingkungan kerja terbaik.
            </p>
          </div>

          <div class="text-center p-6 sm:p-8 rounded-xl hover:shadow-lg transition-all duration-300 group">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-purple-200 transition-colors">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
            <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Proses Cepat</h3>
            <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
              Lamar pekerjaan dengan mudah dan dapatkan respons cepat dari perusahaan melalui platform yang user-friendly.
            </p>
          </div>
        </div>
      </div>
    </section> -->

    <!-- How It Works Section -->
    <section class="py-16 sm:py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 sm:mb-16">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Cara Praktis <span class="text-blue-600">Mendapat Pekerjaan</span>
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            Tiga langkah mudah untuk menemukan pekerjaan impian Anda
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          <div class="relative">
            <div class="text-center">
              <div class="w-16 h-16 sm:w-20 sm:h-20 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 text-xl sm:text-2xl font-bold">
                1
              </div>
              <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Jelajahi Lowongan</h3>
              <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
                Telusuri ribuan lowongan kerja dari berbagai industri dan lokasi sesuai dengan minat dan keahlian Anda.
              </p>
            </div>
            <div class="hidden md:block absolute top-8 sm:top-10 right-0 transform translate-x-1/2">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
              </svg>
            </div>
          </div>

          <div class="relative">
            <div class="text-center">
              <div class="w-16 h-16 sm:w-20 sm:h-20 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 text-xl sm:text-2xl font-bold">
                2
              </div>
              <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Lamar Pekerjaan</h3>
              <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
                Kirim lamaran dengan mudah melalui platform kami. CV dan profil Anda akan langsung terkirim ke perusahaan.
              </p>
            </div>
            <div class="hidden md:block absolute top-8 sm:top-10 right-0 transform translate-x-1/2">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
              </svg>
            </div>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 sm:w-20 sm:h-20 bg-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 text-xl sm:text-2xl font-bold">
              3
            </div>
            <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Mulai Karir Baru</h3>
            <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
              Dapatkan panggilan interview dan mulai perjalanan karir gemilang Anda bersama perusahaan impian.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-16 sm:py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 sm:mb-16">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Apa Kata <span class="text-blue-600">Mereka</span>?
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            Testimoni dari para profesional yang telah menemukan karir impian melalui JobPortal
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          <div class="bg-white p-6 sm:p-8 rounded-xl shadow-sm">
            <div class="flex items-center mb-4">
              <div class="flex text-yellow-400">
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
            </div>
            <p class="text-gray-600 mb-6 italic text-sm sm:text-base">
              "JobPortal membantu saya menemukan pekerjaan impian di perusahaan teknologi terkemuka. Prosesnya sangat mudah dan cepat!"
            </p>
            <div class="flex items-center">
              <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-3 sm:mr-4 text-sm sm:text-base">
                AS
              </div>
              <div>
                <div class="font-semibold text-gray-900 text-sm sm:text-base">Andi Setiawan</div>
                <div class="text-gray-500 text-xs sm:text-sm">Software Developer</div>
              </div>
            </div>
          </div>

          <div class="bg-white p-6 sm:p-8 rounded-xl shadow-sm">
            <div class="flex items-center mb-4">
              <div class="flex text-yellow-400">
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
            </div>
            <p class="text-gray-600 mb-6 italic text-sm sm:text-base">
              "Platform yang sangat user-friendly. Dalam 2 minggu saya sudah mendapat 3 panggilan interview dari perusahaan ternama."
            </p>
            <div class="flex items-center">
              <div class="w-10 h-10 sm:w-12 sm:h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold mr-3 sm:mr-4 text-sm sm:text-base">
                SP
              </div>
              <div>
                <div class="font-semibold text-gray-900 text-sm sm:text-base">Sari Permata</div>
                <div class="text-gray-500 text-xs sm:text-sm">Marketing Manager</div>
              </div>
            </div>
          </div>

          <div class="bg-white p-6 sm:p-8 rounded-xl shadow-sm">
            <div class="flex items-center mb-4">
              <div class="flex text-yellow-400">
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
            </div>
            <p class="text-gray-600 mb-6 italic text-sm sm:text-base">
              "Sebagai fresh graduate, JobPortal memberikan kesempatan yang luar biasa untuk memulai karir di bidang yang saya minati."
            </p>
            <div class="flex items-center">
              <div class="w-10 h-10 sm:w-12 sm:h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold mr-3 sm:mr-4 text-sm sm:text-base">
                RH
              </div>
              <div>
                <div class="font-semibold text-gray-900 text-sm sm:text-base">Rizki Hidayat</div>
                <div class="text-gray-500 text-xs sm:text-sm">Data Analyst</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 sm:py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-6">
          Siap Memulai Karir Gemilang Anda?
        </h2>
        <p class="text-lg sm:text-xl mb-8 text-blue-100">
          Bergabunglah dengan ribuan profesional yang telah menemukan pekerjaan impian mereka melalui JobPortal
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink to="/lowongan" class="btn-primary bg-yellow-400 hover:bg-yellow-500 text-gray-900 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 w-full sm:w-auto">
            Mulai Pencarian Kerja
          </NuxtLink>
          <a href="https://recruitment.gemilanghebat.com/register" target="_blank" rel="noopener noreferrer" class="text-white border-2 border-white hover:bg-white hover:text-blue-700 text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold transition-all duration-300 w-full sm:w-auto text-center">
            Daftar Sekarang
          </a>
        </div>
      </div>
    </section>
    
  </div>
</template>

<script setup>
// Import data
// Load job data
const { default: jobsData } = await import('~/data/data.json')
const allJobs = ref(jobsData)

// Computed properties
const totalJobs = computed(() => allJobs.value.length)

// Job categories with counts and icons
const jobCategories = computed(() => {
  // Create a map of division counts
  const divisiCount = {}
  
  // Count jobs by division
  allJobs.value.forEach(job => {
    const divisi = job.divisi || 'Lainnya'
    divisiCount[divisi] = (divisiCount[divisi] || 0) + 1
  })
  
  // Convert to array and sort by count
  const categories = Object.entries(divisiCount)
    .map(([name, count]) => ({
      name,
      count
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 8) // Show top 8 categories
  
  return categories
})

// Page meta
definePageMeta({
  title: 'JobPortal - Platform Pencarian Kerja Terpercaya'
})

// SEO
useHead({
  title: 'JobPortal - Temukan Karir Gemilang Masa Depan Anda',
  meta: [
    {
      name: 'description',
      content: 'Platform pencarian kerja terpercaya yang menghubungkan talenta terbaik dengan perusahaan impian. Wujudkan karir gemilang bersama ribuan peluang kerja berkualitas di seluruh Indonesia.'
    },
    {
      name: 'keywords',
      content: 'lowongan kerja, pencarian kerja, karir, pekerjaan, job portal, indonesia, fresh graduate, profesional'
    },
    {
      property: 'og:title',
      content: 'JobPortal - Temukan Karir Gemilang Masa Depan Anda'
    },
    {
      property: 'og:description',
      content: 'Platform pencarian kerja terpercaya dengan ribuan lowongan dari perusahaan terkemuka. Mulai perjalanan karir gemilang Anda sekarang!'
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ]
})
</script>