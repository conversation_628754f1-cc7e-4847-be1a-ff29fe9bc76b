<template>
  <div v-if="homepageData">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-32">
        <div class="text-center">
          <h1 class="text-3xl sm:text-4xl md:text-6xl font-bold mb-6 leading-tight" v-html="homepageData.hero.title">
          </h1>
          <p class="text-lg sm:text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
           {{ homepageData.hero.subtitle }}
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <template v-for="button in homepageData.hero.buttons" :key="button.text">
              <NuxtLink
                v-if="button.type === 'primary' || button.type === 'secondary'"
                :to="button.link"
                :class="button.style"
              >
                <svg v-if="button.icon === 'search'" class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
                {{ button.text }}
              </NuxtLink>
              <a
                v-else-if="button.type === 'external'"
                :href="button.link"
                :class="button.style"
                :target="button.target"
                :rel="button.rel"
              >
                {{ button.text }}
              </a>
            </template>
          </div>
        </div>
      </div>

      <!-- Floating Elements -->
      <div
        v-for="(element, index) in homepageData.hero.floatingElements"
        :key="index"
        :class="`absolute ${element.position} ${element.size} ${element.color} rounded-full opacity-20 ${element.animation}`"
      ></div>
    </section>

    <!-- Stats Section -->
    <!-- <section class="py-12 sm:py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-8">
          <div class="text-center">
            <div class="text-2xl sm:text-4xl font-bold text-blue-600 mb-2">{{ totalJobs }}+</div>
            <div class="text-gray-600 font-medium text-sm sm:text-base">Lowongan Aktif</div>
          </div>
          <div class="text-center">
            <div class="text-2xl sm:text-4xl font-bold text-green-600 mb-2">100+</div>
            <div class="text-gray-600 font-medium text-sm sm:text-base">Perusahaan Partner</div>
          </div>
          <div class="text-center">
            <div class="text-2xl sm:text-4xl font-bold text-purple-600 mb-2">10K+</div>
            <div class="text-gray-600 font-medium text-sm sm:text-base">Kandidat Terdaftar</div>
          </div>
          <div class="text-center">
            <div class="text-2xl sm:text-4xl font-bold text-orange-600 mb-2">95%</div>
            <div class="text-gray-600 font-medium text-sm sm:text-base">Tingkat Kepuasan</div>
          </div>
        </div>
      </div>
    </section> -->

    <!-- Quick Job Categories Section -->
    <section class="py-12 sm:py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8 sm:mb-12">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Kategori <span class="text-blue-600">Pekerjaan</span>
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            Temukan lowongan kerja berdasarkan divisi dan kategori yang Anda minati
          </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
          <NuxtLink
            v-for="category in jobCategories"
            :key="category.name"
            :to="`/kategori/${encodeURIComponent(category.name)}`"
            class="group bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 p-4 sm:p-6 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-105"
          >
            <div class="text-center">
              <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-blue-700 transition-colors">
                <!-- Checklist icon SVG - hardcoded to ensure consistent display -->
                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="text-sm sm:text-lg font-semibold text-gray-900 mb-2">{{ category.name }}</h3>
              <p class="text-xs sm:text-sm text-gray-600 mb-3">{{ category.count }} lowongan</p>
              <div class="text-blue-600 text-xs sm:text-sm font-medium group-hover:text-blue-700">
                Lihat Semua →
              </div>
            </div>
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <!-- <section id="features" class="py-16 sm:py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 sm:mb-16">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Mengapa Memilih <span class="text-blue-600">JobPortal</span>?
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            Kami berkomitmen memberikan pengalaman terbaik dalam pencarian kerja dengan fitur-fitur unggulan
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          <div class="text-center p-6 sm:p-8 rounded-xl hover:shadow-lg transition-all duration-300 group">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-blue-200 transition-colors">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
            <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Pencarian Cerdas</h3>
            <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
              Sistem pencarian canggih dengan filter lokasi, pendidikan, dan kriteria lainnya untuk menemukan pekerjaan yang tepat sesuai kualifikasi Anda.
            </p>
          </div>

          <div class="text-center p-6 sm:p-8 rounded-xl hover:shadow-lg transition-all duration-300 group">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-green-200 transition-colors">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Perusahaan Terpercaya</h3>
            <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
              Bermitra dengan perusahaan-perusahaan terkemuka dan startup inovatif yang menawarkan lingkungan kerja terbaik.
            </p>
          </div>

          <div class="text-center p-6 sm:p-8 rounded-xl hover:shadow-lg transition-all duration-300 group">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-purple-200 transition-colors">
              <svg class="w-6 h-6 sm:w-8 sm:h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
            <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">Proses Cepat</h3>
            <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
              Lamar pekerjaan dengan mudah dan dapatkan respons cepat dari perusahaan melalui platform yang user-friendly.
            </p>
          </div>
        </div>
      </div>
    </section> -->

    <!-- How It Works Section -->
    <section class="py-16 sm:py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 sm:mb-16">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4" v-html="homepageData.howItWorks.title">
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            {{ homepageData.howItWorks.subtitle }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          <div
            v-for="(step, index) in homepageData.howItWorks.steps"
            :key="step.id"
            class="relative"
          >
            <div class="text-center">
              <div :class="`w-16 h-16 sm:w-20 sm:h-20 bg-${step.color}-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 text-xl sm:text-2xl font-bold`">
                {{ step.number }}
              </div>
              <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4">{{ step.title }}</h3>
              <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
                {{ step.description }}
              </p>
            </div>
            <div
              v-if="index < homepageData.howItWorks.steps.length - 1"
              class="hidden md:block absolute top-8 sm:top-10 right-0 transform translate-x-1/2"
            >
              <svg :class="`w-6 h-6 sm:w-8 sm:h-8 text-${step.color}-300`" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-16 sm:py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 sm:mb-16">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4" v-html="homepageData.testimonials.title">
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            {{ homepageData.testimonials.subtitle }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          <div
            v-for="testimonial in homepageData.testimonials.items"
            :key="testimonial.id"
            class="bg-white p-6 sm:p-8 rounded-xl shadow-sm"
          >
            <div class="flex items-center mb-4">
              <div class="flex text-yellow-400">
                <svg
                  v-for="star in 5"
                  :key="star"
                  class="w-4 h-4 sm:w-5 sm:h-5"
                  :class="star <= testimonial.rating ? 'fill-current' : 'fill-gray-300'"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
            </div>
            <p class="text-gray-600 mb-6 italic text-sm sm:text-base">
              "{{ testimonial.testimonial }}"
            </p>
            <div class="flex items-center">
              <div :class="`w-10 h-10 sm:w-12 sm:h-12 ${testimonial.avatarColor} rounded-full flex items-center justify-center text-white font-semibold mr-3 sm:mr-4 text-sm sm:text-base`">
                {{ testimonial.avatar }}
              </div>
              <div>
                <div class="font-semibold text-gray-900 text-sm sm:text-base">{{ testimonial.name }}</div>
                <div class="text-gray-500 text-xs sm:text-sm">{{ testimonial.position }}</div>
              </div>
            </div>
          </div>


        </div>
      </div>
    </section>

    <!-- Blog Section -->
    <section class="py-16 sm:py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {{ homepageData.blog.title }}
          </h2>
          <p class="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            {{ homepageData.blog.subtitle }}
          </p>
        </div>

        <!-- Loading State -->
        <div v-if="blogLoading" class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-2 text-gray-600">Memuat artikel...</p>
        </div>

        <!-- Blog Posts -->
        <div v-else-if="featuredBlogPosts.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <article
            v-for="post in featuredBlogPosts"
            :key="post.id"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group cursor-pointer"
            @click="navigateToBlog(post.slug)"
          >
            <div class="relative">
              <img
                :src="post.featuredImage"
                :alt="post.title"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              >
              <div class="absolute top-3 right-3">
                <span class="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
                  {{ post.category }}
                </span>
              </div>
            </div>

            <div class="p-6">
              <div class="flex items-center text-sm text-gray-500 mb-3">
                <time :datetime="post.publishedAt">{{ formatBlogDate(post.publishedAt) }}</time>
                <span class="mx-2">•</span>
                <span>{{ post.readTime }} min read</span>
              </div>

              <h3 class="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
                {{ post.title }}
              </h3>

              <p class="text-gray-600 mb-4 line-clamp-3">
                {{ post.excerpt }}
              </p>

              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-500">{{ post.author }}</span>
                <span class="text-blue-600 font-semibold text-sm group-hover:text-blue-800 transition-colors">
                  Baca Selengkapnya →
                </span>
              </div>
            </div>
          </article>
        </div>

        <!-- View All Blog Button -->
        <div class="text-center">
          <NuxtLink
            :to="homepageData.blog.viewAllLink"
            class="inline-flex items-center bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
            </svg>
            {{ homepageData.blog.viewAllText }}
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 sm:py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-6">
          {{ homepageData.cta.title }}
        </h2>
        <p class="text-lg sm:text-xl mb-8 text-blue-100">
          {{ homepageData.cta.subtitle }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <template v-for="button in homepageData.cta.buttons" :key="button.text">
            <NuxtLink
              v-if="button.type === 'primary'"
              :to="button.link"
              :class="button.style"
            >
              {{ button.text }}
            </NuxtLink>
            <a
              v-else-if="button.type === 'external'"
              :href="button.link"
              :class="button.style"
              :target="button.target"
              :rel="button.rel"
            >
              {{ button.text }}
            </a>
          </template>
        </div>
      </div>
    </section>
    
  </div>
</template>

<script setup>
// Homepage composable
const { fetchHomepageData, loading, error, homepageData } = useHomepage()

// Load homepage data
await fetchHomepageData()

// Import job data (for categories section)
const { default: jobsData } = await import('~/data/data.json')
const allJobs = ref(jobsData)

// Blog composable
const { getFeaturedPosts } = useBlog()

// Blog data
const featuredBlogPosts = ref([])
const blogLoading = ref(false)

// Load featured blog posts
const loadFeaturedBlogPosts = async () => {
  blogLoading.value = true
  try {
    const response = await getFeaturedPosts(3)
    featuredBlogPosts.value = response.data
  } catch (error) {
    console.error('Error loading featured blog posts:', error)
  } finally {
    blogLoading.value = false
  }
}

// Load blog posts on mount
onMounted(() => {
  loadFeaturedBlogPosts()
})

// Computed properties
const totalJobs = computed(() => allJobs.value.length)

// Job categories with counts and icons
const jobCategories = computed(() => {
  // Create a map of division counts
  const divisiCount = {}
  
  // Count jobs by division
  allJobs.value.forEach(job => {
    const divisi = job.divisi || 'Lainnya'
    divisiCount[divisi] = (divisiCount[divisi] || 0) + 1
  })
  
  // Convert to array and sort by count
  const categories = Object.entries(divisiCount)
    .map(([name, count]) => ({
      name,
      count
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 8) // Show top 8 categories
  
  return categories
})

// Helper functions
const navigateToBlog = (slug) => {
  navigateTo(`/blog/${slug}`)
}

const formatBlogDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Get SEO data
const seoData = computed(() => homepageData.value?.seo)

// Page meta
definePageMeta({
  title: computed(() => seoData.value?.pageMetaTitle || 'JobPortal - Platform Pencarian Kerja Terpercaya')
})

// SEO - reactive based on loaded data
useHead(() => ({
  title: seoData.value?.title || 'JobPortal Gemilang - Info Lowongan Kerja di Indonesia',
  link: [
    {
      rel: 'canonical',
      href: 'https://lokergemilang.com/'
    }
  ],
  meta: [
    {
      name: 'description',
      content: seoData.value?.metaDescription || 'Platform pencarian kerja terpercaya yang menghubungkan talenta terbaik dengan perusahaan impian.'
    },
    {
      name: 'keywords',
      content: seoData.value?.keywords || 'lowongan kerja, pencarian kerja, karir, pekerjaan, job portal, indonesia, fresh graduate, profesional'
    },
    {
      property: 'og:title',
      content: seoData.value?.ogTitle || 'JobPortal Gemilang - Info Lowongan Kerja di Indonesia'
    },
    {
      property: 'og:description',
      content: seoData.value?.ogDescription || 'Platform pencarian kerja terpercaya yang menghubungkan talenta terbaik dengan perusahaan impian.'
    },
    {
      property: 'og:type',
      content: seoData.value?.ogType || 'website'
    }
  ]
}))
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>