<template>
  <div>
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Memuat halaman...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <h1 class="text-2xl font-bold text-gray-900 mb-4"><PERSON><PERSON><PERSON><PERSON></h1>
        <p class="text-gray-600 mb-6">{{ error }}</p>
        <button
          @click="loadAboutData"
          class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PERSON>ba <PERSON>gi
        </button>
      </div>
    </div>

    <!-- Content -->
    <div v-else-if="aboutData">
      <!-- Hero Section -->
      <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              {{ aboutData.hero.title }} <span class="text-yellow-300">{{ aboutData.hero.companyName }}</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-4xl mx-auto leading-relaxed">
              {{ aboutData.hero.subtitle }}
            </p>
          </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-300 rounded-full opacity-20 animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-32 h-32 bg-blue-300 rounded-full opacity-20 animate-bounce"></div>
      </section>

      <!-- Company Overview -->
      <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                {{ aboutData.companyOverview.title }} <span class="text-blue-600">{{ aboutData.companyOverview.subtitle }}</span>
              </h2>

              <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  </svg>
                  {{ aboutData.companyOverview.vision.title }}
                </h3>
                <p class="text-gray-600 leading-relaxed text-lg">
                  {{ aboutData.companyOverview.vision.content }}
                </p>
              </div>

              <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                  </svg>
                  {{ aboutData.companyOverview.mission.title }}
                </h3>
                <ul class="space-y-3 text-gray-600 text-lg">
                  <li
                    v-for="(item, index) in aboutData.companyOverview.mission.items"
                    :key="index"
                    class="flex items-start"
                  >
                    <svg class="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    {{ item }}
                  </li>
                </ul>
              </div>
            </div>

            <div class="relative">
              <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8">
                <img
                  :src="aboutData.companyOverview.image.url"
                  :alt="aboutData.companyOverview.image.alt"
                  class="w-full h-80 object-cover rounded-xl shadow-lg"
                >
                <div class="mt-6 text-center">
                  <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ aboutData.companyOverview.image.caption }}</h4>
                  <p class="text-gray-600">{{ aboutData.companyOverview.image.location }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Services Section -->
      <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {{ aboutData.services.title }} <span class="text-blue-600">{{ aboutData.services.subtitle }}</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              {{ aboutData.services.description }}
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Service Cards -->
            <div
              v-for="service in aboutData.services.serviceList"
              :key="service.id"
              class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
            >
              <div :class="`bg-gradient-to-r from-${service.color}-600 to-${service.color}-700 p-6`">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center mr-4">
                    <!-- Building Icon for Facility Services -->
                    <svg v-if="service.icon === 'building'" :class="`w-6 h-6 text-${service.color}-600`" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h1a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                    <!-- Users Icon for Outsourcing Services -->
                    <svg v-else-if="service.icon === 'users'" :class="`w-6 h-6 text-${service.color}-600`" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                  </div>
                  <h3 class="text-2xl font-bold text-white">{{ service.title }}</h3>
                </div>
              </div>
              <div class="p-6">
                <p class="text-gray-600 mb-6 leading-relaxed">
                  {{ service.description }}
                </p>
                <ul class="space-y-3">
                  <li
                    v-for="(feature, index) in service.features"
                    :key="index"
                    class="flex items-center text-gray-700"
                  >
                    <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    {{ feature }}
                  </li>
                </ul>
              </div>
            </div>
        </div>
      </div>
    </section>

    <!-- Company Stats -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {{ aboutData.stats.title }} <span class="text-blue-600">{{ aboutData.stats.subtitle }}</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            {{ aboutData.stats.description }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div
            v-for="achievement in aboutData.stats.achievements"
            :key="achievement.id"
            :class="`text-center p-6 bg-gradient-to-br from-${achievement.color}-50 to-${achievement.color}-100 rounded-xl`"
          >
            <div :class="`text-4xl font-bold text-${achievement.color}-600 mb-2`">{{ achievement.number }}</div>
            <div class="text-gray-700 font-medium">{{ achievement.label }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Values Section -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {{ aboutData.values.title }} <span class="text-blue-600">{{ aboutData.values.subtitle }}</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            {{ aboutData.values.description }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div
            v-for="value in aboutData.values.valueList"
            :key="value.id"
            class="text-center p-8 bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300"
          >
            <div :class="`w-16 h-16 bg-${value.color}-100 rounded-full flex items-center justify-center mx-auto mb-6`">
              <!-- Check Circle Icon for Integritas -->
              <svg v-if="value.icon === 'check-circle'" :class="`w-8 h-8 text-${value.color}-600`" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <!-- Lightning Icon for Kualitas -->
              <svg v-else-if="value.icon === 'lightning'" :class="`w-8 h-8 text-${value.color}-600`" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              <!-- Users Icon for Kemitraan -->
              <svg v-else-if="value.icon === 'users'" :class="`w-8 h-8 text-${value.color}-600`" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ value.title }}</h3>
            <p class="text-gray-600 leading-relaxed">
              {{ value.description }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <!-- <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Tim <span class="text-blue-600">Manajemen</span>
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Dipimpin oleh para profesional berpengalaman yang berkomitmen pada keunggulan layanan
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="relative mb-6">
              <img 
                src="https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=400" 
                alt="CEO Tata Karya Gemilang. PT" 
                class="w-48 h-48 rounded-full mx-auto object-cover shadow-lg"
              >
              <div class="absolute bottom-0 right-1/2 transform translate-x-1/2 translate-y-1/2">
                <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Budi Santoso</h3>
            <p class="text-blue-600 font-medium mb-3">Chief Executive Officer</p>
            <p class="text-gray-600 text-sm leading-relaxed">
              Lebih dari 20 tahun pengalaman di industri facility services dan manajemen operasional.
            </p>
          </div>

          <div class="text-center">
            <div class="relative mb-6">
              <img 
                src="https://images.pexels.com/photos/3760263/pexels-photo-3760263.jpeg?auto=compress&cs=tinysrgb&w=400" 
                alt="COO Tata Karya Gemilang. PT" 
                class="w-48 h-48 rounded-full mx-auto object-cover shadow-lg"
              >
              <div class="absolute bottom-0 right-1/2 transform translate-x-1/2 translate-y-1/2">
                <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Sari Wijaya</h3>
            <p class="text-green-600 font-medium mb-3">Chief Operating Officer</p>
            <p class="text-gray-600 text-sm leading-relaxed">
              Ahli dalam optimalisasi operasional dan pengembangan sistem manajemen kualitas.
            </p>
          </div>

          <div class="text-center">
            <div class="relative mb-6">
              <img 
                src="https://images.pexels.com/photos/3760067/pexels-photo-3760067.jpeg?auto=compress&cs=tinysrgb&w=400" 
                alt="HRD Tata Karya Gemilang. PT" 
                class="w-48 h-48 rounded-full mx-auto object-cover shadow-lg"
              >
              <div class="absolute bottom-0 right-1/2 transform translate-x-1/2 translate-y-1/2">
                <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                </div>
              </div>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Ahmad Rahman</h3>
            <p class="text-purple-600 font-medium mb-3">Head of Human Resources</p>
            <p class="text-gray-600 text-sm leading-relaxed">
              Spesialis dalam pengembangan SDM dan strategi talent management untuk pertumbuhan perusahaan.
            </p>
          </div>
        </div>
      </div>
    </section> -->

      <!-- CTA Section -->
      <section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            {{ aboutData.cta.title }}
          </h2>
          <p class="text-xl mb-8 text-blue-100">
            {{ aboutData.cta.description }}
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <NuxtLink
              v-for="button in aboutData.cta.buttons"
              :key="button.text"
              :to="button.type === 'primary' ? button.link : undefined"
              :href="button.type === 'secondary' ? button.link : undefined"
              :class="`text-lg px-8 py-4 rounded-full font-semibold transition-all duration-300 ${button.style} ${button.type === 'primary' ? 'shadow-lg hover:shadow-xl transform hover:scale-105' : ''}`"
            >
              {{ button.text }}
            </NuxtLink>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
// Import composable
const { fetchAboutData, loading, error, aboutData } = useAbout()

// Page meta (static)
definePageMeta({
  title: 'Tentang Tata Karya Gemilang. PT - Facility Services & Outsourcing'
})

// Load about data
const loadAboutData = async () => {
  try {
    await fetchAboutData()
  } catch (err) {
    console.error('Error loading about data:', err)
  }
}

// Load data on mount
await loadAboutData()

// Get SEO data after loading
const seoData = computed(() => aboutData.value?.seo)

// SEO - reactive based on loaded data
useHead(() => ({
  title: seoData.value?.title || 'Tentang Tata Karya Gemilang. PT - Perusahaan Facility Services & Outsourcing Terpercaya',
  link: [
    {
      rel: 'canonical',
      href: 'https://lokergemilang.com/tentang'
    }
  ],
  meta: [
    {
      name: 'description',
      content: seoData.value?.metaDescription || 'Tata Karya Gemilang. PT adalah perusahaan terdepan dalam layanan facility services dan outsourcing di Indonesia.'
    },
    {
      name: 'keywords',
      content: seoData.value?.keywords || 'Tata Karya Gemilang. PT, facility services, outsourcing, cleaning service, security, maintenance'
    },
    {
      property: 'og:title',
      content: seoData.value?.ogTitle || 'Tentang Tata Karya Gemilang. PT - Facility Services & Outsourcing Terpercaya'
    },
    {
      property: 'og:description',
      content: seoData.value?.ogDescription || 'Perusahaan terdepan dalam layanan facility services dan outsourcing di Indonesia.'
    },
    {
      property: 'og:type',
      content: seoData.value?.ogType || 'website'
    }
  ]
}))
</script>